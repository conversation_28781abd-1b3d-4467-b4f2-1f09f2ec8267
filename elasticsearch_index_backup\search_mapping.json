{"search": {"mappings": {"properties": {"attributes": {"dynamic": "true", "properties": {"color": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}}}, "availability": {"properties": {"available_days": {"type": "keyword"}, "available_times": {"type": "keyword"}, "booking_lead_time": {"type": "keyword"}, "in_stock": {"type": "boolean"}, "requires_appointment": {"type": "boolean"}, "stock_quantity": {"type": "integer"}}}, "brand": {"type": "keyword"}, "category": {"type": "text", "fields": {"hierarchy": {"type": "keyword"}, "keyword": {"type": "keyword"}}, "analyzer": "standard"}, "contact": {"properties": {"email": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "phone": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "website": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "created_at": {"type": "date", "format": "date_optional_time"}, "delivery_type": {"type": "keyword"}, "description": {"type": "text", "analyzer": "english"}, "food_details": {"properties": {"allergens": {"type": "keyword"}, "dietary_restrictions": {"type": "keyword"}, "ingredients": {"type": "keyword"}, "nutritional_info": {"type": "object", "dynamic": "true"}, "shelf_life": {"type": "text"}, "storage_instructions": {"type": "text"}}}, "hours": {"properties": {"friday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "monday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "saturday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sunday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "thursday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tuesday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "wednesday": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "hs_code": {"type": "keyword"}, "images": {"type": "nested", "properties": {"alt_text": {"type": "text"}, "is_primary": {"type": "boolean"}, "url": {"type": "keyword"}}}, "links": {"type": "nested", "properties": {"description": {"type": "text"}, "title": {"type": "text"}, "type": {"type": "keyword"}, "url": {"type": "keyword"}}}, "location": {"properties": {"address": {"type": "text"}, "city": {"type": "keyword"}, "coordinates": {"type": "geo_point"}, "country": {"type": "keyword"}, "service_area": {"type": "keyword"}, "state": {"type": "keyword"}, "zip_code": {"type": "keyword"}}}, "manufacturer": {"type": "keyword"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}, "analyzer": "my_analyzer"}, "place_type": {"type": "keyword"}, "price": {"properties": {"amount": {"type": "float"}, "currency": {"type": "keyword"}}}, "pricing": {"properties": {"currency": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "pricing_model": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "rate": {"type": "float"}}}, "promotions": {"type": "nested", "properties": {"active": {"type": "boolean"}, "description": {"type": "text"}, "end_date": {"type": "date", "format": "date_optional_time"}, "offer": {"properties": {"currency": {"type": "keyword"}, "discount_amount": {"type": "float"}, "discount_percentage": {"type": "float"}}}, "start_date": {"type": "date", "format": "date_optional_time"}, "title": {"type": "text"}, "type": {"type": "keyword"}}}, "provider": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "rating": {"properties": {"average": {"type": "float"}, "count": {"type": "integer"}}}, "related_product": {"type": "keyword"}, "reviews": {"type": "nested", "properties": {"comment": {"type": "text"}, "date": {"type": "date", "format": "yyyy-MM-dd"}, "rating": {"type": "integer"}, "user_id": {"type": "keyword"}}}, "seller_type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "service_type": {"type": "keyword"}, "services_offered": {"type": "keyword"}, "sku": {"type": "keyword"}, "store_id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tags": {"type": "text", "fields": {"keyword": {"type": "keyword"}}, "analyzer": "standard"}, "type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "upc": {"type": "keyword"}, "updated_at": {"type": "date", "format": "date_optional_time"}, "videos": {"type": "nested", "properties": {"description": {"type": "text"}, "duration": {"type": "keyword"}, "thumbnail": {"type": "keyword"}, "title": {"type": "text"}, "url": {"type": "keyword"}}}}}}}