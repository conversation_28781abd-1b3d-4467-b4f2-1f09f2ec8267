#!/usr/bin/env python3
"""
Quick script to restore Elasticsearch index from backup file
"""
import json
import requests
import sys
from pathlib import Path

def convert_to_bulk_format(input_file, output_file):
    """Convert Elasticsearch export format to bulk import format"""
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line in infile:
            if line.strip():
                doc = json.loads(line.strip())
                
                # Extract the document data
                index_action = {
                    "index": {
                        "_index": doc["_index"],
                        "_id": doc["_id"]
                    }
                }
                
                # Write the index action
                outfile.write(json.dumps(index_action) + '\n')
                
                # Write the document source
                outfile.write(json.dumps(doc["_source"]) + '\n')

def import_to_elasticsearch(bulk_file, es_url="http://localhost:9200"):
    """Import bulk data to Elasticsearch"""
    url = f"{es_url}/_bulk"
    
    with open(bulk_file, 'rb') as f:
        headers = {'Content-Type': 'application/x-ndjson'}
        response = requests.post(url, data=f, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('errors'):
                print("Some errors occurred during import:")
                for item in result['items']:
                    if 'index' in item and 'error' in item['index']:
                        print(f"Error: {item['index']['error']}")
            else:
                print(f"Successfully imported {len(result['items'])} documents")
                return True
        else:
            print(f"Failed to import data. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    return False

def main():
    # File paths
    backup_file = Path("elasticsearch_index_backup/search")
    bulk_file = Path("search_bulk_quick.json")
    
    if not backup_file.exists():
        print(f"Backup file not found: {backup_file}")
        sys.exit(1)
    
    print("Converting backup file to bulk format...")
    convert_to_bulk_format(backup_file, bulk_file)
    
    print("Importing data to Elasticsearch...")
    if import_to_elasticsearch(bulk_file):
        print("Data restoration completed successfully!")
        
        # Clean up temporary file
        bulk_file.unlink()
    else:
        print("Data restoration failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
