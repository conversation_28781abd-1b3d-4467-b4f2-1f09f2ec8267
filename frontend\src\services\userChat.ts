import axios from 'axios';

export interface UserChatMessage {
  id: string;
  role: 'user' | 'other';
  content: string;
  timestamp: Date;
  sender: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export interface ChatRoom {
  id: string;
  name: string;
  participants: {
    id: string;
    name: string;
    avatar?: string;
    online: boolean;
  }[];
  lastMessage?: UserChatMessage;
}

// Mock data for demonstration
const mockRooms: ChatRoom[] = [
  {
    id: '1',
    name: 'General',
    participants: [
      { id: 'user1', name: '<PERSON>', online: true },
      { id: 'user2', name: '<PERSON>', online: false },
      { id: 'user3', name: '<PERSON>', online: true }
    ]
  },
  {
    id: '2',
    name: 'Project Team',
    participants: [
      { id: 'user1', name: '<PERSON>', online: true },
      { id: 'user4', name: '<PERSON>', online: true }
    ]
  }
];

const mockMessages: { [roomId: string]: UserChatMessage[] } = {
  '1': [
    {
      id: '1',
      role: 'other',
      content: 'Hey everyone! How\'s the project going?',
      timestamp: new Date(Date.now() - 3600000),
      sender: { id: 'user2', name: '<PERSON>' }
    },
    {
      id: '2',
      role: 'user',
      content: 'Going well! Just finished the UI mockups.',
      timestamp: new Date(Date.now() - 1800000),
      sender: { id: 'user1', name: 'John Doe' }
    },
    {
      id: '3',
      role: 'other',
      content: 'Great! Can you share them in the project channel?',
      timestamp: new Date(Date.now() - 900000),
      sender: { id: 'user3', name: 'Bob Wilson' }
    }
  ],
  '2': [
    {
      id: '4',
      role: 'other',
      content: 'The backend API is ready for testing.',
      timestamp: new Date(Date.now() - 7200000),
      sender: { id: 'user4', name: 'Alice Brown' }
    }
  ]
};

export const chatApi = axios.create({
  baseURL: import.meta.env.VITE_CHAT_SERVICE_URL || 'http://localhost:8002',
  headers: {
    'Content-Type': 'application/json',
  },
});

export const userChat = {
  // Get all chat rooms
  getRooms: async (): Promise<ChatRoom[]> => {
    try {
      // For now, return mock data
      // In a real implementation, this would make an API call
      return mockRooms;
    } catch (error) {
      console.error('Error fetching chat rooms:', error);
      return mockRooms; // Fallback to mock data
    }
  },

  // Get messages for a specific room
  getMessages: async (roomId: string): Promise<UserChatMessage[]> => {
    try {
      // For now, return mock data
      // In a real implementation, this would make an API call
      return mockMessages[roomId] || [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return mockMessages[roomId] || [];
    }
  },

  // Send a message to a room
  sendMessage: async (roomId: string, content: string): Promise<UserChatMessage> => {
    try {
      const newMessage: UserChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content,
        timestamp: new Date(),
        sender: { id: 'current-user', name: 'You' }
      };

      // For now, just add to mock data
      // In a real implementation, this would make an API call
      if (!mockMessages[roomId]) {
        mockMessages[roomId] = [];
      }
      mockMessages[roomId].push(newMessage);

      // Simulate a response after a delay
      setTimeout(() => {
        const responses = [
          "That's interesting!",
          "I agree with that.",
          "Thanks for sharing!",
          "Good point!",
          "Let me think about that.",
          "Sounds good to me!"
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        const responseMessage: UserChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'other',
          content: randomResponse,
          timestamp: new Date(),
          sender: { 
            id: 'bot-user', 
            name: mockRooms.find(r => r.id === roomId)?.participants[1]?.name || 'Other User'
          }
        };
        
        mockMessages[roomId].push(responseMessage);
      }, 1000 + Math.random() * 2000);

      return newMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  // Subscribe to real-time updates (mock implementation)
  subscribeToRoom: (roomId: string, _callback: (message: UserChatMessage) => void) => {
    // In a real implementation, this would set up WebSocket or SSE connection
    console.log(`Subscribed to room ${roomId}`);
    
    // Return unsubscribe function
    return () => {
      console.log(`Unsubscribed from room ${roomId}`);
    };
  }
};
