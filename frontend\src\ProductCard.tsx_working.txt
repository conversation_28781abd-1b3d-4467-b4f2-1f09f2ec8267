import React from 'react';
import { Heart, Share2, CheckCircle } from 'lucide-react';

const ProductCard = () => {
  return (
    <div className="max-w-md rounded-lg border border-gray-200 bg-white shadow-md overflow-hidden">
      {/* Header with badges */}
      <div className="relative p-3 flex justify-between">
        <span className="bg-amber-400 text-white px-3 py-1 rounded-md font-medium">Best Seller</span>
        <span className="bg-pink-400 text-white px-3 py-1 rounded-md font-medium">Product</span>
      </div>
      
      {/* Product title */}
      <div className="px-4 pt-1 pb-2">
        <h2 className="text-lg font-bold text-gray-900">UV Protection Sunglasses For Men & Women | Black Brown Green Solid Full</h2>
      </div>
      
      {/* Product image and QR code */}
      <div className="px-4 flex">
        <div className="w-1/4">
          <img src="/api/placeholder/100/100" alt="QR code" className="w-full" />
        </div>
        <div className="w-3/4 flex justify-center">
          <img src="/api/placeholder/200/100" alt="Sunglasses" className="h-32 object-contain" />
        </div>
      </div>
      
      {/* Rating stars */}
      <div className="px-4 py-2 flex items-center">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <svg key={star} className="w-5 h-5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          ))}
        </div>
        <div className="ml-3 flex space-x-1">
          {[1, 2, 3, 4, 5].map((dot, i) => (
            <div key={dot} className={`w-2 h-2 rounded-full ${i === 2 ? 'bg-gray-400' : 'bg-gray-200'}`}></div>
          ))}
        </div>
      </div>
      
      {/* Price information */}
      <div className="px-4 pt-1">
        <div className="flex items-end">
          <span className="text-2xl font-bold">Rs. 2999</span>
          <span className="ml-2 text-sm font-medium text-green-600">(60% OFF)</span>
        </div>
        <div className="text-xs text-gray-500">MRP ₹999.00</div>
      </div>
      
      {/* Rewards information */}
      <div className="px-4 py-2 text-xs">
        <div>Get up to 3% back + rewards worth ₹1800</div>
        <div>FREE Delivery by <span className="font-medium">Seller</span></div>
      </div>
      
      {/* Savings badge */}
      <div className="px-4 py-2 flex justify-end">
        <div className="flex items-center bg-teal-500 text-white px-3 py-1 rounded-full text-sm">
          <CheckCircle className="w-4 h-4 mr-1" />
          <span>Save Rs. 99/-</span>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="px-4 py-2 grid grid-cols-2 gap-2">
        <button className="bg-teal-500 text-white py-2 px-4 rounded-md font-medium">View Details</button>
        <button className="bg-yellow-400 text-black py-2 px-4 rounded-md font-medium">Add to Cart</button>
      </div>
      
      {/* Footer with location and seller */}
      <div className="px-4 py-3 flex justify-between items-center border-t border-gray-200">
        <div className="flex items-start">
          <div className="text-red-500 mr-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path>
            </svg>
          </div>
          <div>
            <div className="font-medium">0.4 km</div>
            <div className="text-xs">Offered By</div>
            <div className="font-medium">Ray Ban Sunglasses</div>
          </div>
        </div>
        <div>
          <div className="text-sm mb-1">Get it by Today@ 5PM</div>
          <div className="text-xs text-blue-600 text-right">Disclaimer</div>
        </div>
      </div>
      
      {/* Share and like buttons */}
      <div className="px-4 py-2 flex justify-between border-t border-gray-200">
        <button className="text-purple-400">
          <Share2 className="w-6 h-6" />
        </button>
        <button className="text-red-500">
          <Heart className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default ProductCard;