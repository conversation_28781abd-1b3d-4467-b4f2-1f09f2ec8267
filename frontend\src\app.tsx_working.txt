import React from 'react';
import VerticalMenu from './components/VerticalMenu';
import StatusBar from './components/StatusBar';
import SearchBar from './components/SearchBar';
import AddressBar from './components/AddressBar';
import TabBar from './components/TabBar';
import ChatInterface from './components/ChatInterface';
import AIChatInterface from './components/AIChatInterface';

function App() {
  return (
    <div className="p-0 flex h-screen overflow-hidden">
      {/* Left Sidebar - Vertical Menu */}
      <div className="w-12 border-r border-gray-200 bg-white h-full">
        <VerticalMenu />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col h-full">
        {/* Top Tab Bar */}
        <div className="border-b border-gray-200">
          <TabBar />
        </div>

        {/* Address Bar */}
        <div className="border-b border-gray-200">
          <AddressBar />
        </div>

        {/* Search Bar and Chat Interface */}
        <div className="flex-1 flex py-0 px-0 items-start">
          {/* Search Bar */}
          <div className="flex-1 p-2">
            <SearchBar />
            {/* Results Area */}
            <div className="flex-1 py-4 px-0 overflow-auto">
              {/* Placeholder for Results */}
              <div className="text-gray-500 text-sm">
                <span>
                  {/* Grid of Service Cards */}
                  <div className="grid grid-cols-4 gap-4">
                    {Array(12).fill(0).map((_, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-md p-4 h-32 flex items-center justify-center">
                        <span className="text-gray-400">Service Card {index + 1}</span>
                      </div>
                    ))}
                  </div>

                  {/* Pagination */}
                  <div className="flex items-center justify-center mt-6 space-x-1">
                    <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">
                      <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button className="w-8 h-8 flex items-center justify-center rounded-md bg-purple-100 text-purple-700">1</button>
                    <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">2</button>
                    <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">3</button>
                    <span className="px-2">...</span>
                    <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">12</button>
                    <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">
                      <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </span>
              </div>
            </div>
          </div>

          {/* Chat Interfaces */}
          <div className="w-96 border-l border-gray-200 bg-white h-full flex flex-col">
            {/* AI Chat Interface */}
            <div className="flex-1 border-b border-gray-200">
              <ChatInterface />
            </div>

            {/* People Chat Interface */}
            <div className="flex-1">
              <AIChatInterface />
            </div>
          </div>
        </div>

        {/* Status Bar at Bottom */}
        <div className="border-t border-gray-200">
          <StatusBar />
        </div>
      </div>

      {/* Right Sidebar - Vertical Menu */}
      <div className="w-12 border-l border-gray-200 bg-white h-full">
        <VerticalMenu />
      </div>
    </div>
  );
}

export default App;