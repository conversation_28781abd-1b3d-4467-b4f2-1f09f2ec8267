import React, { useState } from 'react';
import { Mic, Paperclip, Send, Settings } from 'lucide-react';

interface AIConfig {
  mode: 'chat' | 'completion' | 'agent';
  model: string;
  mcp: string;
  browsing: boolean;
  tools: boolean;
}

interface AIInputBarProps {
  onSubmit?: (text: string, files: File[], config: AIConfig) => void;
  onSettingsClick?: () => void;
  config: AIConfig;
  onConfigChange?: (config: AIConfig) => void;
  showConfigPanel?: boolean;
  isLoading?: boolean;
}

const AIInputBar: React.FC<AIInputBarProps> = ({
  onSubmit,
  onSettingsClick,
  config,
  onConfigChange,
  showConfigPanel = false
}) => {
  const [text, setText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [localShowConfig, setLocalShowConfig] = useState(showConfigPanel);

  // Update local state when prop changes
  React.useEffect(() => {
    setLocalShowConfig(showConfigPanel);
  }, [showConfigPanel]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (text.trim() || files.length > 0) {
      onSubmit?.(text, files, config);
      setText('');
      setFiles([]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const handleConfigChange = (key: keyof AIConfig, value: string | boolean) => {
    onConfigChange?.({
      ...config,
      [key]: value
    });
  };

  const toggleConfig = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newShowConfig = !localShowConfig;
    setLocalShowConfig(newShowConfig);
    onSettingsClick?.();
    return false;
  };

  return (
    <div className="w-full max-w-3xl" onClick={(e) => e.stopPropagation()}>
      {localShowConfig && (
        <div className="bg-white/80 backdrop-blur-lg border border-gray-200 rounded-lg shadow-lg p-3 mb-2 space-y-2">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 w-20">Mode:</label>
            <select
              value={config.mode}
              onChange={(e) => handleConfigChange('mode', e.target.value)}
              className="flex-1 rounded-md border border-gray-300 px-2 py-1 text-sm"
            >
              <option value="chat">Chat</option>
              <option value="completion">Completion</option>
              <option value="agent">Agent</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 w-20">Model:</label>
            <select
              value={config.model}
              onChange={(e) => handleConfigChange('model', e.target.value)}
              className="flex-1 rounded-md border border-gray-300 px-2 py-1 text-sm"
            >
              <option value="gpt-4">GPT-4</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="claude-3-opus">Claude 3 Opus</option>
              <option value="claude-3-sonnet">Claude 3 Sonnet</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 w-20">MCP:</label>
            <select
              value={config.mcp}
              onChange={(e) => handleConfigChange('mcp', e.target.value)}
              className="flex-1 rounded-md border border-gray-300 px-2 py-1 text-sm"
            >
              <option value="default">Default</option>
              <option value="coding">Coding</option>
              <option value="analysis">Analysis</option>
              <option value="creative">Creative</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 w-20">Features:</label>
            <div className="flex-1 flex items-center space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={config.browsing}
                  onChange={(e) => handleConfigChange('browsing', e.target.checked)}
                  className="rounded border-gray-300 text-purple-400 focus:ring-purple-400"
                />
                <span className="ml-2 text-sm text-gray-700">Web Browsing</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={config.tools}
                  onChange={(e) => handleConfigChange('tools', e.target.checked)}
                  className="rounded border-gray-300 text-purple-400 focus:ring-purple-400"
                />
                <span className="ml-2 text-sm text-gray-700">Tools</span>
              </label>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white/80 backdrop-blur-lg border border-gray-200 rounded-lg shadow-lg p-3 flex items-center space-x-3">
        <div
          role="button"
          className="p-2 hover:bg-gray-100 rounded-full cursor-pointer"
          onClick={() => {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.onchange = (e) => handleFileChange(e as any);
            input.click();
          }}
        >
          <Paperclip className="w-5 h-5 text-gray-500" />
        </div>
        
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          className="flex-1 bg-transparent border-none outline-none resize-none py-1"
          rows={1}
        />

        <div
          role="button"
          className={`p-2 rounded-full cursor-pointer ${
            isRecording ? 'bg-red-100 text-red-500' : 'hover:bg-gray-100'
          }`}
          onClick={() => setIsRecording(!isRecording)}
        >
          <Mic className="w-5 h-5" />
        </div>

        <div
          role="button"
          tabIndex={0}
          className={`p-2 hover:bg-gray-100 rounded-full cursor-pointer ${localShowConfig ? 'bg-purple-100 text-purple-400' : ''}`}
          onClick={toggleConfig}
          onKeyPress={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              toggleConfig(e as any);
            }
          }}
        >
          <Settings className="w-5 h-5 text-gray-500" />
        </div>

        <div
          role="button"
          className="p-2 bg-purple-400 hover:bg-purple-700 rounded-full text-white cursor-pointer"
          onClick={handleSubmit}
        >
          <Send className="w-5 h-5" />
        </div>
      </div>
    </div>
  );
};

export default AIInputBar; 