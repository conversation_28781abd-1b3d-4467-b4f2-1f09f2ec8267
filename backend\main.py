from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.modules.search.router import router as search_router
from server import api_router

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.DEBUG
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(search_router, prefix="/api/search", tags=["Search"])
app.include_router(api_router)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": settings.APP_VERSION
    }

# Test search endpoint
@app.get("/test-search")
async def test_search(query: str = "coffee"):
    try:
        response = await es_client.search(
            index=settings.ELASTICSEARCH_INDEX,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["name^2", "description"]
                                }
                            }
                        ]
                    }
                },
                "from": 0,
                "size": 5
            }
        )

        hits = response["hits"]["hits"]
        total = response["hits"]["total"]["value"]
        items = [hit["_source"] for hit in hits]

        return {
            "total": total,
            "items": items[:2]  # Return only first 2 items for testing
        }
    except Exception as e:
        return {"error": str(e)}

import os
import redis.asyncio as redis
import elasticsearch
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379')
elasticsearch_url = os.environ.get('ELASTICSEARCH_URL', 'http://localhost:9200')

# Global clients
redis_client = None
es_client = None

@app.on_event("startup")
async def startup_event():
    global redis_client, es_client
    
    # Connect to Redis
    try:
        redis_client = redis.from_url(redis_url, decode_responses=True)
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
    
    # Connect to Elasticsearch
    try:
        es_client = elasticsearch.AsyncElasticsearch([elasticsearch_url])
        if await es_client.ping():
            logger.info("Connected to Elasticsearch successfully")
        else:
            logger.error("Failed to connect to Elasticsearch")
    except Exception as e:
        logger.error(f"Elasticsearch connection error: {e}")

    try:
        if not await es_client.indices.exists(index=settings.ELASTICSEARCH_INDEX):
            await es_client.indices.create(index=settings.ELASTICSEARCH_INDEX)
            logger.info(f"Elasticsearch index '{settings.ELASTICSEARCH_INDEX}' created successfully")
    except Exception as e:
        logger.error(f"Failed to create Elasticsearch index '{settings.ELASTICSEARCH_INDEX}': {e}")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, es_client
    if redis_client:
        await redis_client.close()
    if es_client:
        await es_client.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)