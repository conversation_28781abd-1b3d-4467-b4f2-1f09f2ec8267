import React, { useState } from 'react';
import { 
  Search,
  Cpu,
  Smartphone,
  Car,
  Palette,
  Code2,
  Bot,
  Heart,
  DollarSign,
  GraduationCap,
  MessageCircle,
  Users,
  Tv,
  // Business icons
  Receipt,
  UserCircle,
  ShoppingCart,
  Megaphone,
  Globe,
  Briefcase,
  Box,
  Layout,
  ChevronRight,
  X
} from 'lucide-react';

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  children?: MenuItem[];
}

// Define the business categories
const businessCategories: MenuItem[] = [
  { 
    id: 'finance', 
    label: 'Finance', 
    icon: <Receipt size={20} />,
    children: [
      {
        id: 'accounting',
        label: 'Accounting',
        icon: <DollarSign size={20} />,
        children: [
          { id: 'transactions', label: 'Transactions', icon: <Receipt size={20} /> },
          { id: 'reports', label: 'Reports', icon: <Layout size={20} /> }
        ]
      },
      {
        id: 'invoicing',
        label: 'Invoicing',
        icon: <Receipt size={20} />,
        children: [
          { id: 'create', label: 'Create Invoice', icon: <Receipt size={20} /> },
          { id: 'history', label: 'Invoice History', icon: <Layout size={20} /> }
        ]
      }
    ]
  },
  { 
    id: 'hr', 
    label: 'Human Resources', 
    icon: <UserCircle size={20} />,
    children: [
      {
        id: 'employees',
        label: 'Employees',
        icon: <Users size={20} />,
        children: [
          { id: 'directory', label: 'Directory', icon: <UserCircle size={20} /> },
          { id: 'onboarding', label: 'Onboarding', icon: <UserCircle size={20} /> }
        ]
      }
    ]
  },
  { 
    id: 'sales', 
    label: 'Sales', 
    icon: <ShoppingCart size={20} />,
    children: [
      {
        id: 'orders',
        label: 'Orders',
        icon: <ShoppingCart size={20} />,
        children: [
          { id: 'new', label: 'New Orders', icon: <ShoppingCart size={20} /> },
          { id: 'history', label: 'Order History', icon: <Layout size={20} /> }
        ]
      }
    ]
  },
  { id: 'marketing', label: 'Marketing', icon: <Megaphone size={20} /> },
  { id: 'websites', label: 'Websites', icon: <Globe size={20} /> },
  { id: 'services', label: 'Services', icon: <Briefcase size={20} /> },
  { id: 'supply', label: 'Supply Chain', icon: <Box size={20} /> },
  { id: 'productivity', label: 'Productivity', icon: <Layout size={20} /> }
];

const personalCategories: MenuItem[] = [
  { id: 'iot', label: 'IoT', icon: <Cpu size={20} /> },
  { id: 'devices', label: 'Devices', icon: <Smartphone size={20} /> },
  { id: 'mobility', label: 'Mobility', icon: <Car size={20} /> },
  { id: 'design', label: 'Design', icon: <Palette size={20} /> },
  { id: 'code', label: 'Code', icon: <Code2 size={20} /> },
  { id: 'ai_automation', label: 'AI Automation', icon: <Bot size={20} /> },
  { id: 'health', label: 'Health', icon: <Heart size={20} /> },
  { id: 'finance', label: 'Finance', icon: <DollarSign size={20} /> },
  { id: 'education', label: 'Education', icon: <GraduationCap size={20} /> },
  { id: 'communication', label: 'Communication', icon: <MessageCircle size={20} /> },
  { id: 'social', label: 'Social', icon: <Users size={20} /> },
  { id: 'entertainment', label: 'Entertainment', icon: <Tv size={20} /> }
];

interface VerticalMenuProps {
  onCategorySelect?: (category: string) => void;
  isBusinessMode?: boolean;
}

const VerticalMenu: React.FC<VerticalMenuProps> = ({ 
  onCategorySelect,
  isBusinessMode = false
}) => {
  const [selectedMainItem, setSelectedMainItem] = useState<string | null>(null);
  const [selectedSubItem, setSelectedSubItem] = useState<string | null>(null);
  const [selectedSubSubItem, setSelectedSubSubItem] = useState<string | null>(null);
  const [isMenuExpanded, setIsMenuExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);

  const handleMainItemClick = (item: MenuItem) => {
    if (selectedMainItem === item.id) {
      setSelectedMainItem(null);
      setSelectedSubItem(null);
      setSelectedSubSubItem(null);
    } else {
      setSelectedMainItem(item.id);
      setSelectedSubItem(null);
      setSelectedSubSubItem(null);
      setIsMenuExpanded(true);
    }
    onCategorySelect?.(item.id);
  };

  const handleSubItemClick = (itemId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedSubItem(selectedSubItem === itemId ? null : itemId);
    setSelectedSubSubItem(null);
  };

  const handleSubSubItemClick = (itemId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedSubSubItem(selectedSubSubItem === itemId ? null : itemId);
  };

  const handleSearchClick = () => {
    setIsSearchActive(true);
    setIsMenuExpanded(true);
  };

  const handleSearchClose = () => {
    setIsSearchActive(false);
    setSearchQuery('');
  };

  const toggleMenu = () => {
    setIsMenuExpanded(!isMenuExpanded);
    if (!isMenuExpanded) {
      setSelectedMainItem(null);
      setSelectedSubItem(null);
      setSelectedSubSubItem(null);
      setIsSearchActive(false);
      setSearchQuery('');
    }
  };

  const categories = isBusinessMode ? businessCategories : personalCategories;
  const filteredCategories = categories.filter(category => {
    if (!searchQuery) return true;
    
    // Search in main category
    if (category.label.toLowerCase().includes(searchQuery.toLowerCase())) return true;
    
    // Search in subcategories
    if (category.children?.some(sub => 
      sub.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sub.children?.some(subSub => 
        subSub.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    )) return true;
    
    return false;
  });

  return (
    <div 
      className={`h-full bg-white border-r border-gray-200 flex flex-col relative transition-all duration-300 ${
        isMenuExpanded ? 'w-64' : 'w-16'
      }`}
      onMouseEnter={() => !isSearchActive && setIsMenuExpanded(true)}
      onMouseLeave={() => !isSearchActive && setIsMenuExpanded(false)}
    >
      {/* Search Bar */}
      <div className="p-2 border-b border-gray-200">
        <div className="relative group">
          {!isSearchActive ? (
            <button 
              className="w-full p-2 rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center"
              onClick={handleSearchClick}
            >
              <Search className="w-5 h-5 text-gray-500" />
              <span className={`ml-3 text-gray-700 transition-opacity duration-200 ${
                isMenuExpanded ? 'opacity-100' : 'opacity-0 w-0'
              }`}>
                Search Menu
              </span>
              {!isMenuExpanded && (
                <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 pointer-events-none">
                  <div className="bg-gray-900 text-white text-sm px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    Search Menu
                  </div>
                </div>
              )}
            </button>
          ) : (
            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search..."
                  className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent text-sm"
                  autoFocus
                />
                <Search className="w-4 h-4 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2" />
              </div>
              <button
                onClick={handleSearchClose}
                className="p-2 rounded-lg hover:bg-gray-100"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Categories List */}
      <nav className="flex-1 overflow-y-auto py-2">
        <ul className="space-y-1 px-2">
          {filteredCategories.map((category) => (
            <li key={category.id}>
              <div className="relative group">
                <button
                  onClick={() => handleMainItemClick(category)}
                  className={`w-full p-2 rounded-lg transition-colors flex items-center justify-between ${
                    selectedMainItem === category.id
                      ? 'bg-purple-50 text-purple-400'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center min-w-0">
                    <span className="flex-shrink-0">{category.icon}</span>
                    <span className={`ml-3 transition-opacity duration-200 truncate ${
                      isMenuExpanded ? 'opacity-100' : 'opacity-0 w-0'
                    }`}>
                      {category.label}
                    </span>
                  </div>
                  {category.children && isMenuExpanded && (
                    <ChevronRight
                      size={16}
                      className={`transform transition-transform ${
                        selectedMainItem === category.id ? 'rotate-90' : ''
                      }`}
                    />
                  )}
                </button>

                {/* Tooltip for collapsed state */}
                {!isMenuExpanded && (
                  <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 pointer-events-none">
                    <div className="bg-gray-900 text-white text-sm px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      {category.label}
                    </div>
                  </div>
                )}

                {/* Submenu */}
                {isMenuExpanded && selectedMainItem === category.id && category.children && (
                  <ul className="mt-1 ml-6 space-y-1">
                    {category.children.map((subItem) => (
                      <li key={subItem.id}>
                        <button
                          onClick={(e) => handleSubItemClick(subItem.id, e)}
                          className={`w-full p-2 rounded-lg transition-colors flex items-center justify-between ${
                            selectedSubItem === subItem.id
                              ? 'bg-purple-50 text-purple-400'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <div className="flex items-center min-w-0">
                            <span className="flex-shrink-0">{subItem.icon}</span>
                            <span className="ml-3 truncate">{subItem.label}</span>
                          </div>
                          {subItem.children && (
                            <ChevronRight
                              size={16}
                              className={`transform transition-transform ${
                                selectedSubItem === subItem.id ? 'rotate-90' : ''
                              }`}
                            />
                          )}
                        </button>

                        {/* Sub-submenu */}
                        {selectedSubItem === subItem.id && subItem.children && (
                          <ul className="mt-1 ml-6 space-y-1">
                            {subItem.children.map((subSubItem) => (
                              <li key={subSubItem.id}>
                                <button
                                  onClick={(e) => handleSubSubItemClick(subSubItem.id, e)}
                                  className={`w-full p-2 rounded-lg transition-colors flex items-center ${
                                    selectedSubSubItem === subSubItem.id
                                      ? 'bg-purple-50 text-purple-400'
                                      : 'text-gray-700 hover:bg-gray-100'
                                  }`}
                                >
                                  <span className="flex-shrink-0">{subSubItem.icon}</span>
                                  <span className="ml-3 truncate">{subSubItem.label}</span>
                                </button>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </li>
          ))}
        </ul>
      </nav>

      {/* Toggle Button */}
      <div className="p-2 border-t border-gray-200">
        <button
          onClick={toggleMenu}
          className="w-full p-2 rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center"
        >
          <ChevronRight
            className={`w-5 h-5 text-gray-500 transform transition-transform duration-300 ${
              isMenuExpanded ? 'rotate-180' : ''
            }`}
          />
        </button>
      </div>
    </div>
  );
};

export default VerticalMenu;
