#!/usr/bin/env python3
"""
Test script for AI service chat endpoint
"""
import requests
import json
import os

def test_ai_service():
    """Test the AI service chat endpoint"""
    
    # AI service URL
    ai_service_url = "http://localhost:8001"
    
    # Test data
    test_request = {
        "messages": [
            {
                "role": "user",
                "content": "Hello, can you help me with a simple question?"
            }
        ],
        "context": {
            "browsing": False,
            "tools": False
        }
    }
    
    try:
        print("Testing AI service chat endpoint...")
        print(f"URL: {ai_service_url}/chat")
        print(f"Request: {json.dumps(test_request, indent=2)}")
        
        # Make the request
        response = requests.post(
            f"{ai_service_url}/chat",
            json=test_request,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
            print("\n✅ Test PASSED - AI service is working correctly!")
        else:
            print(f"Response Text: {response.text}")
            print(f"\n❌ Test FAILED - Status code: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("\n❌ Test FAILED - Could not connect to AI service")
        print("Make sure the AI service is running on http://localhost:8001")
    except requests.exceptions.Timeout:
        print("\n❌ Test FAILED - Request timed out")
    except Exception as e:
        print(f"\n❌ Test FAILED - Error: {e}")

def test_health_endpoint():
    """Test the health endpoint"""
    ai_service_url = "http://localhost:8001"
    
    try:
        print("\nTesting health endpoint...")
        response = requests.get(f"{ai_service_url}/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"Health Status: {json.dumps(health_data, indent=2)}")
            print("✅ Health check PASSED")
        else:
            print(f"❌ Health check FAILED - Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check FAILED - Error: {e}")

if __name__ == "__main__":
    print("AI Service Test Script")
    print("=" * 50)
    
    # Check environment variables
    print("\nEnvironment Variables:")
    print(f"OPENAI_API_KEY: {'Set' if os.getenv('OPENAI_API_KEY') else 'Not set'}")
    print(f"SARVAM_API_KEY: {'Set' if os.getenv('SARVAM_API_KEY') else 'Not set'}")
    
    # Run tests
    test_health_endpoint()
    test_ai_service()
