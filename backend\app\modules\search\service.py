from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from app.core.config import settings
from app.modules.search.schemas import (
    SearchRequest,
    SearchResponse,
    Item,
    SearchType
)
from elasticsearch import AsyncElasticsearch

class SearchService:
    def __init__(self):
        self.es_client = AsyncElasticsearch([settings.ELASTICSEARCH_URL])

    async def search(self, request: SearchRequest) -> SearchResponse:
        """
        Perform search using Elasticsearch with optional geospatial filtering
        """
        # Build base query
        query_body = {
            "bool": {
                "must": [
                    {
                        "multi_match": {
                            "query": request.query,
                            "fields": ["name^2", "description"]
                        }
                    }
                ]
            }
        }

        # Add type filter if specified
        if request.type != SearchType.ALL:
            query_body["bool"]["filter"] = [{"term": {"type": request.type.lower()}}]

        # Add location filter if coordinates provided
        if request.location and request.radius_km:
            geo_filter = {
                "geo_distance": {
                    "distance": f"{request.radius_km}km",
                    "location": {
                        "lat": request.location.lat,
                        "lon": request.location.lng
                    }
                }
            }
            if "filter" not in query_body["bool"]:
                query_body["bool"]["filter"] = []
            query_body["bool"]["filter"].append(geo_filter)

        # Calculate pagination
        from_ = (request.page - 1) * request.per_page
        size = request.per_page

        try:
            # Execute search
            response = await self.es_client.search(
                index=settings.ELASTICSEARCH_INDEX,
                body={
                    "query": query_body,
                    "from": from_,
                    "size": size
                }
            )

            # Process results
            hits = response["hits"]["hits"]
            total = response["hits"]["total"]["value"]
            items = [hit["_source"] for hit in hits]

            return SearchResponse(
                items=items,
                total=total,
                page=request.page,
                per_page=request.per_page
            )

        except Exception as e:
            raise Exception(f"Search error: {str(e)}")

    async def index_item(self, item: Item) -> bool:
        """
        Index a single item in Elasticsearch
        """
        try:
            # Convert item to dictionary
            item_dict = item.dict()
            
            # Add location field if coordinates exist
            if item.location and item.location.coordinates:
                item_dict["location"] = {
                    "lat": item.location.coordinates.lat,
                    "lon": item.location.coordinates.lng
                }

            # Index document
            await self.es_client.index(
                index=settings.ELASTICSEARCH_INDEX,
                id=item.id,
                body=item_dict
            )
            return True
        except Exception as e:
            raise Exception(f"Indexing error: {str(e)}")

    async def reindex_all(self) -> Tuple[int, int]:
        """
        Reindex all items
        """
        success = 0
        errors = 0
        # Implementation depends on your data source
        # This is a placeholder for the reindexing logic
        return success, errors 