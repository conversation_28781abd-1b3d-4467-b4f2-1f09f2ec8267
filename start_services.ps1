# Create and activate Python virtual environment
python -m venv venv
.\venv\Scripts\Activate

# Install backend dependencies
cd backend
pip install -r requirements.txt
cd ..

# Install A2A service dependencies
cd backend/a2a_service
pip install -r requirements.txt
cd ../..

# Start services in separate windows
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend/a2a_service; uvicorn main:app --port 8001 --reload"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; uvicorn main:app --port 8000 --reload"

# Start frontend
cd frontend
npm run dev 