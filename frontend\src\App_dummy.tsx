// Dummy App component - not used

function App() {
  return (
    <div className="flex h-screen overflow-hidden">          
      {/* right Sidebar - Vertical Menu */}
      <div className="w-16 border-l border-r border-gray-200 bg-white h-full flex flex-col items-center py-4">
        <button className="w-8 h-8 mb-4 rounded-full border border-gray-300 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
          </svg>
        </button>
        <button className="w-8 h-8 mb-4 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="3" y1="12" x2="21" y2="12" />
            <line x1="3" y1="6" x2="21" y2="6" />
            <line x1="3" y1="18" x2="21" y2="18" />
          </svg>
        </button>
        <button className="w-8 h-8 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button>
      </div>
      {/* Main Content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Top Tabs */}
        <div className="flex border-b border-gray-200 bg-gray-50 px-2 py-1">
          <div className="flex space-x-2 items-center">
            <div className="flex items-center px-3 py-1 rounded-md bg-white border border-purple-200">
              <svg className="w-5 h-5 text-purple-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                <circle cx="8.5" cy="8.5" r="1.5" />
                <polyline points="21 15 16 10 5 21" />
              </svg>
              <span className="text-gray-800">Icons</span>
            </div>
            <div className="flex items-center px-3 py-1">
              <svg className="w-5 h-5 text-purple-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
                <line x1="12" y1="22.08" x2="12" y2="12" />
              </svg>
              <span className="text-gray-400">Collections</span>
            </div>
            <div className="flex items-center px-3 py-1">
              <svg className="w-5 h-5 text-purple-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" />
              </svg>
              <span className="text-gray-400">Favorites</span>
            </div>
          </div>
          
          <div className="ml-auto flex items-center space-x-2">
            <button className="p-1 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              </svg>
            </button>
            <button className="p-1 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
              </svg>
            </button>
            <button className="p-1 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                <line x1="9" y1="3" x2="9" y2="21" />
              </svg>
            </button>
          </div>
        </div>
        {/* Personal/Business Toggle */}
        <div className="py-2 px-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex bg-gray-100 p-1 rounded-full">
            <button className="px-4 py-1 rounded-full bg-brand-gradient text-white">Personal</button>
            <button className="px-4 py-1 rounded-full text-gray-700">Business</button>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full text-sm">
              <span>Productivity</span>
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6 9 12 15 18 9" />
              </svg>
            </div>
            
            <div className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full text-sm">
              <span>Theme</span>
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6 9 12 15 18 9" />
              </svg>
            </div>
            
            <button className="w-8 h-8 flex items-center justify-center text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="16" x2="12" y2="12" />
                <line x1="12" y1="8" x2="12.01" y2="8" />
              </svg>
            </button>
            
            <button className="w-8 h-8 flex items-center justify-center text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
                <path d="M13.73 21a2 2 0 0 1-3.46 0" />
              </svg>
            </button>
            
            <div className="px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-md">
              SOS
            </div>
            
            <div className="w-8 h-8 bg-gray-200 rounded-full overflow-hidden">
              <img src="/api/placeholder/32/32" alt="Avatar" className="w-full h-full object-cover" />
            </div>
          </div>
        </div>
        
        {/* Address Bar */}
        <div className="flex items-center space-x-2 px-4 py-2 border-b border-gray-200">
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15 18 9 12 15 6" />
            </svg>
          </button>
          
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
          
          <div className="flex-1 bg-white border border-gray-300 rounded-full px-4 py-1 text-sm text-gray-500">
            https://aura.ai/services
          </div>
          
          <button className="w-8 h-8 flex items-center justify-center bg-brand-gradient text-white rounded-full">
            Go
          </button>
          
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
            </svg>
          </button>
          
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
            </svg>
          </button>
          
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="18" cy="5" r="3" />
              <circle cx="6" cy="12" r="3" />
              <circle cx="18" cy="19" r="3" />
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" />
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" />
            </svg>
          </button>
          
          <button className="text-gray-400">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3" />
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
            </svg>
          </button>
        </div>
        {/* Search Bar */}
        <div className="py-4 px-6 border-b border-gray-200">
          <div className="relative flex items-center mb-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="11" cy="11" r="8" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" />
                </svg>
              </div>
              <input
                type="text"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
                placeholder="What do you want to accomplish...."
              />
            </div>
            <div className="ml-2">
              <button className="bg-brand-gradient text-white px-8 py-3 rounded-full">
                Go
              </button>
            </div>
            <div className="ml-2">
              <button className="bg-purple-400 text-white px-4 py-3 rounded-full flex items-center">
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="4" y1="6" x2="20" y2="6" />
                  <line x1="4" y1="12" x2="20" y2="12" />
                  <line x1="4" y1="18" x2="11" y2="18" />
                </svg>
                Categories
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 overflow-x-auto">
            <button className="flex items-center space-x-1 px-4 py-2 bg-purple-400 text-white rounded-full whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="7" height="7" />
                <rect x="14" y="3" width="7" height="7" />
                <rect x="14" y="14" width="7" height="7" />
                <rect x="3" y="14" width="7" height="7" />
              </svg>
              <span>All</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
              </svg>
              <span>Business</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
              </svg>
              <span>Communication</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 19l7-7 3 3-7 7-3-3z" />
                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" />
                <path d="M2 2l7.586 7.586" />
                <circle cx="11" cy="11" r="2" />
              </svg>
              <span>Design</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="16 18 22 12 16 6" />
                <polyline points="8 6 2 12 8 18" />
              </svg>
              <span>Development</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
                <line x1="12" y1="18" x2="12.01" y2="18" />
              </svg>
              <span>Device</span>
            </button>
            
            <button className="flex items-center space-x-1 px-4 py-2 bg-white border border-gray-200 rounded-full text-gray-700 whitespace-nowrap">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" />
              </svg>
              <span>Education</span>
            </button>
          </div>
        </div>
        {/* Content Area */}
        <div className="flex-1 overflow-auto p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-gray-500">52 results found</div>
            <div className="flex space-x-2">
              <button className="px-4 py-1 bg-purple-400 text-white rounded-md flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add Service</span>
              </button>
              <button className="px-4 py-1 bg-white border border-gray-200 rounded-md flex items-center space-x-2">
                <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                <span>Filter</span>
              </button>
              <button className="px-4 py-1 bg-white border border-gray-200 rounded-md flex items-center space-x-2">
                <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                <span>Sort</span>
              </button>
            </div>
          </div>
          
          {/* Grid of Service Cards */}
          <div className="grid grid-cols-4 gap-4">
            {Array(12).fill(0).map((_, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-md p-4 h-32 flex items-center justify-center">
                <span className="text-gray-400">Service Card {index + 1}</span>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="flex items-center justify-center mt-6 space-x-1">
            <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">
              <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button className="w-8 h-8 flex items-center justify-center rounded-md bg-purple-100 text-purple-700">1</button>
            <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">2</button>
            <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">3</button>
            <span className="px-2">...</span>
            <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">12</button>
            <button className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-gray-100">
              <svg className="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Status Bar at Bottom */}
        <div className="border-t border-gray-200 py-1 px-4 flex items-center">
          <div className="flex items-center space-x-2">
            <button className="w-6 h-6 rounded-full flex items-center justify-center text-gray-500">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10" />
              </svg>
            </button>
            <button className="w-6 h-6 flex items-center justify-center text-gray-500">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="1" />
                <circle cx="12" cy="5" r="1" />
                <circle cx="12" cy="19" r="1" />
              </svg>
            </button>
          </div>
          
          <div className="flex items-center space-x-2 mx-auto">
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="5" />
                <line x1="12" y1="1" x2="12" y2="3" />
                <line x1="12" y1="21" x2="12" y2="23"/>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
                <line x1="1" y1="12" x2="3" y2="12" />
                <line x1="21" y1="12" x2="23" y2="12" />
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
              </svg>
              <span>72°F, Sunny</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
              <span>3:34:59 PM</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                <polyline points="22 4 12 14.01 9 11.01" />
              </svg>
              <span>Good</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="4" y="4" width="16" height="16" rx="2" ry="2" />
                <rect x="9" y="9" width="6" height="6" />
                <line x1="9" y1="1" x2="9" y2="4" />
                <line x1="15" y1="1" x2="15" y2="4" />
                <line x1="9" y1="20" x2="9" y2="23" />
                <line x1="15" y1="20" x2="15" y2="23" />
                <line x1="20" y1="9" x2="23" y2="9" />
                <line x1="20" y1="14" x2="23" y2="14" />
                <line x1="1" y1="9" x2="4" y2="9" />
                <line x1="1" y1="14" x2="4" y2="14" />
              </svg>
              <span>32% CPU</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 7h-7m-7 10h7m-9-7h4m13-3v18M6 22V6c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v2M2 18v-3a2 2 0 0 1 2-2h2" />
              </svg>
              <span>62% GPU</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M5 12.55a11 11 0 0 1 14.08 0" />
                <path d="M1.42 9a16 16 0 0 1 21.16 0" />
                <path d="M8.53 16.11a6 6 0 0 1 6.95 0" />
                <line x1="12" y1="20" x2="12.01" y2="20" />
              </svg>
              <span>Connected</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="1" y="6" width="18" height="12" rx="2" ry="2" />
                <line x1="23" y1="13" x2="23" y2="11" />
              </svg>
              <span>85%</span>
            </div>
            
            <div className="flex items-center px-2 py-1 text-xs border border-gray-200 rounded-md">
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M17 22v-4h-2l2.3-5.7a1 1 0 0 0-1.36-1.24L13 13.85V7l2.96-2.41A1 1 0 0 0 14.7 3L12 5v1.5L7.5 4.09A1 1 0 0 0 6.16 5.3L9 9v4.85l-2.94-2.79a1 1 0 0 0-1.36 1.24L7 18h-2v4" />
              </svg>
              <span>500B Trees Planted</span>
            </div>
          </div>
          
          <div className="ml-auto text-green-600 font-medium">
            <span>Trees</span>
          </div>
        </div>
      </div>
      
      {/* Right Sidebar - Chat */}
      <div className="w-80 border-l border-gray-200 bg-white">
        {/* AI Assistant Chat */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center mr-3">
                <span className="text-purple-800 font-bold text-sm">AI</span>
              </div>
              <h3 className="font-bold">Co-pilot Assistant</h3>
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="11" cy="11" r="8" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" />
                </svg>
              </button>
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="space-y-3 mb-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">Hello! How can I assist you today?</p>
            </div>
            
            <div className="bg-gray-200 rounded-lg p-3 ml-auto">
              <p className="text-gray-800">I need help with the dashboard setup.</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">Hello! How can I assist you today?</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">I'll guide you through the dashboard setup process. What specific aspect would you like to know about?</p>
            </div>
            
            <div className="bg-gray-200 rounded-lg p-3 ml-auto">
              <p className="text-gray-800">I need help with the dashboard setup.</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">I'll guide you through the dashboard setup process. What specific aspect would you like to know about?</p>
            </div>
          </div>
          
          <div className="relative">
            <input
              type="text"
              className="w-full px-4 py-2 pr-10 bg-gray-100 border border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
              placeholder="Type a message..."
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 2L11 13" />
                <path d="M22 2l-7 20-4-9-9-4 20-7z" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* User Chat */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-200 rounded-full overflow-hidden mr-3">
                <img src="/api/placeholder/32/32" alt="User" className="w-full h-full object-cover" />
              </div>
              <div>
                <h3 className="font-bold">John Doe</h3>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                </svg>
              </button>
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="23 7 16 12 23 17 23 7" />
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2" />
                </svg>
              </button>
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="11" cy="11" r="8" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" />
                </svg>
              </button>
              <button className="text-gray-400">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="space-y-3 mb-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">Hello! How can I assist you today?</p>
            </div>
            
            <div className="bg-gray-200 rounded-lg p-3 ml-auto">
              <p className="text-gray-800">I need help with the dashboard setup.</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">I'll guide you through the dashboard setup process. What specific aspect would you like to know about?</p>
            </div>
            
            <div className="bg-gray-200 rounded-lg p-3 ml-auto">
              <p className="text-gray-800">I need help with the dashboard setup.</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-gray-800">I'll guide you through the dashboard setup process. What specific aspect would you like to know about?</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <button className="mr-2 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 2L11 13" />
                <path d="M22 2l-7 20-4-9-9-4 20-7z" />
              </svg>
            </button>
            <div className="relative flex-1">
              <input
                type="text"
                className="w-full px-4 py-2 pr-10 bg-gray-100 border border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
                placeholder="Type a message..."
              />
            </div>
            <button className="ml-2 text-gray-500">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 2L11 13" />
                <path d="M22 2l-7 20-4-9-9-4 20-7z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      {/* right Sidebar - Vertical Menu */}
      <div className="w-16 border-l border-r border-gray-200 bg-white h-full flex flex-col items-center py-4">
        <button className="w-8 h-8 mb-4 rounded-full border border-gray-300 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
          </svg>
        </button>
        <button className="w-8 h-8 mb-4 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="3" y1="12" x2="21" y2="12" />
            <line x1="3" y1="6" x2="21" y2="6" />
            <line x1="3" y1="18" x2="21" y2="18" />
          </svg>
        </button>
        <button className="w-8 h-8 flex items-center justify-center text-gray-400">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button>
      </div>
    </div>
  );
}

export default App;