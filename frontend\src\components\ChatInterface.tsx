import React, { useState, useEffect, useRef } from 'react';
import { MessageSquare, Send, Users, Settings } from 'lucide-react';
import { userChat, UserChatMessage, ChatRoom } from '../services/userChat';

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<UserChatMessage[]>([]);
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [currentRoom, setCurrentRoom] = useState<ChatRoom | null>(null);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load rooms on component mount
  useEffect(() => {
    const loadRooms = async () => {
      try {
        const roomList = await userChat.getRooms();
        setRooms(roomList);
        if (roomList.length > 0) {
          setCurrentRoom(roomList[0]);
        }
      } catch (error) {
        console.error('Error loading rooms:', error);
      }
    };

    loadRooms();
  }, []);

  // Load messages when room changes
  useEffect(() => {
    if (currentRoom) {
      const loadMessages = async () => {
        try {
          const roomMessages = await userChat.getMessages(currentRoom.id);
          setMessages(roomMessages);
        } catch (error) {
          console.error('Error loading messages:', error);
        }
      };

      loadMessages();
    }
  }, [currentRoom]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || !currentRoom || isLoading) return;

    setIsLoading(true);
    try {
      const newMessage = await userChat.sendMessage(currentRoom.id, inputText.trim());
      setMessages(prev => [...prev, newMessage]);
      setInputText('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-3">
            <MessageSquare className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {currentRoom?.name || 'Chat'}
            </h2>
            <p className="text-sm text-gray-500">
              {currentRoom?.participants.filter(p => p.online).length || 0} online
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
            <Users className="w-5 h-5" />
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Room Selector */}
      {rooms.length > 1 && (
        <div className="p-2 border-b border-gray-200 bg-gray-50">
          <select
            value={currentRoom?.id || ''}
            onChange={(e) => {
              const room = rooms.find(r => r.id === e.target.value);
              setCurrentRoom(room || null);
            }}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:border-blue-400"
          >
            {rooms.map(room => (
              <option key={room.id} value={room.id}>
                {room.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-scroll p-4 space-y-4 min-h-0">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className="max-w-[80%] space-y-1">
              {message.role === 'other' && (
                <div className="text-xs text-gray-500 px-1">
                  {message.sender.name}
                </div>
              )}
              <div
                className={`rounded-lg px-4 py-2 ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-400 to-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.content}
              </div>
              <div className="text-xs text-gray-400 px-1">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 flex-shrink-0">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={`Message ${currentRoom?.name || 'chat'}...`}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-400"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputText.trim() || isLoading}
            className="px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
          >
            <Send className="w-4 h-4" />
            <span>Send</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
