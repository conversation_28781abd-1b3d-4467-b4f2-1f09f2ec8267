from elasticsearch import Elasticsearch
import asyncio

async def migrate_data():
    # Connect to old Elasticsearch
    old_es = Elasticsearch(["http://localhost:9200"])
    
    # Connect to new Elasticsearch
    new_es = Elasticsearch(["http://localhost:9200"])  # Same instance for now
    
    try:
        # Get all documents from old index
        result = old_es.search(
            index="search",
            body={
                "query": {"match_all": {}},
                "size": 10000  # Adjust if you have more documents
            }
        )
        
        print(f"Found {len(result['hits']['hits'])} documents to migrate")
        
        # Create index with mapping if it doesn't exist
        if not new_es.indices.exists(index="search"):
            mapping = {
                "mappings": {
                    "properties": {
                        "name": {"type": "text"},
                        "description": {"type": "text"},
                        "type": {"type": "keyword"},
                        "category": {"type": "keyword"},
                        "tags": {"type": "keyword"},
                        "location": {
                            "type": "geo_point"
                        },
                        "price": {
                            "properties": {
                                "amount": {"type": "float"},
                                "currency": {"type": "keyword"}
                            }
                        },
                        "rating": {
                            "properties": {
                                "average": {"type": "float"},
                                "count": {"type": "integer"}
                            }
                        }
                    }
                }
            }
            new_es.indices.create(index="search", body=mapping)
            print("Created new index with mapping")
        
        # Migrate each document
        for hit in result['hits']['hits']:
            try:
                doc = hit['_source']
                doc_id = hit['_id']
                
                # Transform location data if needed
                if 'location' in doc:
                    if isinstance(doc['location'], dict) and 'coordinates' in doc['location']:
                        coords = doc['location']['coordinates']
                        if isinstance(coords, dict) and 'lat' in coords and ('lng' in coords or 'lon' in coords):
                            doc['location'] = {
                                "lat": coords['lat'],
                                "lon": coords.get('lon', coords.get('lng'))
                            }
                    elif isinstance(doc['location'], dict) and 'lat' in doc['location'] and ('lon' in doc['location'] or 'lng' in doc['location']):
                        doc['location'] = {
                            "lat": doc['location']['lat'],
                            "lon": doc['location'].get('lon', doc['location'].get('lng'))
                        }
                
                # Index document in new Elasticsearch
                new_es.index(
                    index="search",
                    id=doc_id,
                    body=doc
                )
                print(f"Migrated document {doc_id}")
            except Exception as e:
                print(f"Error migrating document {doc_id}: {str(e)}")
                print(f"Document content: {doc}")
        
        print("Migration completed successfully")
        
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        old_es.close()
        new_es.close()

if __name__ == "__main__":
    asyncio.run(migrate_data()) 