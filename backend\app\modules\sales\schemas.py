from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class LeadStatus(str, Enum):
    NEW = "new"
    CONTACTED = "contacted"
    QUALIFIED = "qualified"
    PROPOSAL = "proposal"
    NEGOTIATION = "negotiation"
    WON = "won"
    LOST = "lost"

class CustomerBase(BaseModel):
    name: str
    email: EmailStr
    phone: str
    company: Optional[str] = None
    industry: Optional[str] = None
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class Customer(CustomerBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class AppointmentBase(BaseModel):
    customer_id: str
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    meeting_link: Optional[str] = None

class AppointmentCreate(AppointmentBase):
    pass

class Appointment(AppointmentBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SalesLeadBase(BaseModel):
    customer_id: Optional[str] = None
    company_name: str
    contact_name: str
    contact_email: EmailStr
    contact_phone: str
    source: str
    estimated_value: Optional[float] = Field(None, ge=0)
    notes: Optional[str] = None

class SalesLeadCreate(SalesLeadBase):
    pass

class SalesLead(SalesLeadBase):
    id: str
    status: LeadStatus = LeadStatus.NEW
    created_at: datetime
    updated_at: datetime
    last_contact_date: Optional[datetime] = None

    class Config:
        from_attributes = True 