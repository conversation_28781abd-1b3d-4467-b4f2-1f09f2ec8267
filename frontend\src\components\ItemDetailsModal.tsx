import React from 'react';
// Only import the most essential icons
import { X } from 'lucide-react';

// Interface definitions remain the same
interface Image {
  url: string;
  is_primary?: boolean;
  alt_text?: string;
}

interface Location {
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
}

interface Rating {
  average: number;
  count: number;
}

interface Item {
  id?: string;
  _id?: string;
  store_id?: string;
  type?: 'product' | 'service' | 'store';
  name: string;
  description: string;
  images?: Image[];
  category?: string[];
  tags?: string[];
  location?: Location;
  rating?: Rating;
  created_at?: string;
  updated_at?: string;
  price?: {
    amount: number;
    currency: string;
    discount_percentage?: number;
    original_amount?: number;
    tax_included?: boolean;
  };
  availability?: {
    in_stock?: boolean;
    stock_quantity?: number;
    available_days?: string[];
    available_times?: string[];
    requires_appointment?: boolean;
    booking_lead_time?: string;
  };
  brand?: string;
  manufacturer?: string;
  sku?: string;
  upc?: string;
  attributes?: Record<string, any>;
  service_type?: string;
  provider?: string;
  pricing?: {
    rate: number;
    currency: string;
    pricing_model: string;
  };
  seller_type?: string;
  place_type?: string;
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  hours?: Record<string, string>;
  services_offered?: string[];
}

// Type guards
const isProduct = (item: Item): boolean => {
  return item.type === 'product' || !!item.price;
};

const isService = (item: Item): boolean => {
  return item.type === 'service' || (!!item.pricing && !!item.service_type);
};

const isStore = (item: Item): boolean => {
  return item.type === 'store' || !!item.place_type;
};

interface ItemDetailsModalProps {
  open: boolean;
  onClose: () => void;
  item: Item | null;
  loading: boolean;
}

const ItemDetailsModal: React.FC<ItemDetailsModalProps> = ({
  open,
  onClose,
  item,
  loading
}) => {
  if (!open) return null;
  
  console.log("Modal is open:", open);
  console.log("Item data:", item);
  console.log("Loading state:", loading);
  
  // Get primary image URL
  const getPrimaryImage = (item: Item) => {
    if (!item?.images || item.images.length === 0) {
      return '/api/placeholder/400/400';
    }
    
    const primaryImage = item.images.find(img => img.is_primary);
    return primaryImage ? primaryImage.url : item.images[0].url;
  };

  // Show loading state
  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-4xl mx-4 md:mx-auto max-h-[90vh] overflow-y-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      return dateString;
    }
  };

  // Get location string
  const getLocationString = (item: Item) => {
    if (!item?.location) return 'Location not available';
    
    const parts = [];
    if (item.location.address) parts.push(item.location.address);
    if (item.location.city) parts.push(item.location.city);
    if (item.location.state) parts.push(item.location.state);
    if (item.location.country) parts.push(item.location.country);
    if (item.location.zip_code) parts.push(item.location.zip_code);
    
    return parts.join(', ');
  };

  // Render loading state
  if (loading || !item) {
    return (
      <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${open ? '' : 'hidden'}`}>
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-11/12 max-h-[90vh] overflow-hidden">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gray-900"></div>
            <div className="ml-4 text-xl font-semibold">Loading details...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${open ? '' : 'hidden'}`}>
      <div className="bg-white rounded-lg border border-purple-200 shadow-md max-w-4xl w-11/12 max-h-[90vh] overflow-auto">
        {/* Header with close button */}
        <div className="relative p-4 border-b border-purple-200">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{item.name}</h2>
              <div className="flex flex-wrap gap-2 mt-2">
                <span className={`px-2 py-1 rounded-md text-sm font-medium ${isProduct(item) ? 'bg-green-100 text-green-800' : isService(item) ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>
                  {isProduct(item) ? "📦 Product" : isService(item) ? "🔧 Service" : "🏪 Store"}
                </span>
                {item.category?.map((cat, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 rounded-md text-gray-700 text-sm">
                    {cat}
                  </span>
                ))}
              </div>
            </div>
            <button onClick={onClose} className="text-purple-400 hover:text-purple-700">
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - image and basic info */}
            <div>
              <div className="rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                <img 
                  src={getPrimaryImage(item)} 
                  alt={item.name} 
                  className="max-h-80 object-contain w-full"
                />
              </div>
              
              {/* Image gallery */}
              {item.images && item.images.length > 1 && (
                <div className="flex gap-2 mt-3 overflow-x-auto pb-2">
                  {item.images.map((image, index) => (
                    <div key={index} className="w-16 h-16 flex-shrink-0 rounded-md overflow-hidden bg-gray-100">
                      <img 
                        src={image.url} 
                        alt={image.alt_text || `${item.name} image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}
              
              {/* Basic Info */}
              <div className="mt-4">
                {/* Price Display */}
                {item.price && (
                  <div className="flex items-end mb-3">
                    <span className="text-2xl font-bold text-gray-900">
                      {item.price.currency || 'INR'} {item.price.amount?.toFixed(2)}
                    </span>
                    {isProduct(item) && item.price.discount_percentage && (
                      <span className="ml-2 text-sm font-medium text-green-600">
                        ({item.price.discount_percentage}% OFF)
                      </span>
                    )}
                  </div>
                )}
                
                {/* Description */}
                <p className="text-gray-700 my-4">{item.description}</p>
                
                {/* Location */}
                {item.location && (
                  <div className="flex items-start mt-3 text-gray-600">
                    <span className="text-gray-500 mr-2 flex-shrink-0 mt-0.5">📍</span>
                    <span>{getLocationString(item)}</span>
                  </div>
                )}
                
                {/* Rating */}
                {item.rating && (
                  <div className="flex items-center mt-3">
                    <div className="flex text-yellow-400">
                      {/* Simple text stars instead of icon component */}
                      <span className="text-yellow-400">
                        {"★".repeat(Math.round(item.rating.average))}
                        {"☆".repeat(5 - Math.round(item.rating.average))}
                      </span>
                    </div>
                    <span className="ml-2 text-sm text-gray-600">
                      {item.rating.average.toFixed(1)} ({item.rating.count} reviews)
                    </span>
                  </div>
                )}
                
                {/* Dates */}
                <div className="mt-3 text-sm text-gray-500">
                  <div className="flex items-center">
                    <span className="mr-2">📅</span>
                    <span>Created: {formatDate(item.created_at)}</span>
                  </div>
                  {item.updated_at && (
                    <div className="flex items-center mt-1">
                      <span className="mr-2">📅</span>
                      <span>Updated: {formatDate(item.updated_at)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* Right column - details */}
            <div>
              {/* Store-specific details */}
              {isStore(item) && (
                <>
                  {/* Contact Information */}
                  {item.contact && (
                    <div className="mb-6 space-y-3">
                      <h3 className="text-lg font-semibold border-b pb-2">Contact Information</h3>
                      {item.contact.phone && (
                        <div className="flex">
                          <span className="font-medium w-1/3 text-gray-600">Phone:</span>
                          <span className="w-2/3">{item.contact.phone}</span>
                        </div>
                      )}
                      {item.contact.email && (
                        <div className="flex">
                          <span className="font-medium w-1/3 text-gray-600">Email:</span>
                          <span className="w-2/3">{item.contact.email}</span>
                        </div>
                      )}
                      {item.contact.website && (
                        <div className="flex">
                          <span className="font-medium w-1/3 text-gray-600">Website:</span>
                          <span className="w-2/3">
                            <a href={item.contact.website} target="_blank" rel="noopener noreferrer" className="text-purple-400 hover:text-purple-800">
                              {item.contact.website}
                            </a>
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Business Hours */}
                  {item.hours && Object.keys(item.hours).length > 0 && (
                    <div className="mb-6 space-y-3">
                      <h3 className="text-lg font-semibold border-b pb-2">Business Hours</h3>
                      {Object.entries(item.hours).map(([day, hours]) => (
                        <div key={day} className="flex">
                          <span className="font-medium w-1/3 text-gray-600">{day}:</span>
                          <span className="w-2/3">{hours}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Services Offered */}
                  {item.services_offered && item.services_offered.length > 0 && (
                    <div className="mb-6 space-y-3">
                      <h3 className="text-lg font-semibold border-b pb-2">Services Offered</h3>
                      <div className="flex flex-wrap gap-2">
                        {item.services_offered.map((service, index) => (
                          <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Store Details */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold border-b pb-2">Store Details</h3>
                    {item.seller_type && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Seller Type:</span>
                        <span className="w-2/3">{item.seller_type}</span>
                      </div>
                    )}
                    {item.place_type && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Place Type:</span>
                        <span className="w-2/3">{item.place_type}</span>
                      </div>
                    )}
                    {item.store_id && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Store ID:</span>
                        <span className="w-2/3">{item.store_id}</span>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Product-specific details */}
              {isProduct(item) && (
                <>
                  {/* Availability */}
                  {item.availability && (
                    <div className="mb-4">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                        item.availability.in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {item.availability.in_stock ? "✓" : "✗"} 
                        {item.availability.in_stock 
                          ? `In Stock (${item.availability.stock_quantity} available)` 
                          : "Out of Stock"
                        }
                      </span>
                    </div>
                  )}
                  
                  {/* Product details */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold border-b pb-2">Product Details</h3>
                    
                    {item.brand && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Brand:</span>
                        <span className="w-2/3">{item.brand}</span>
                      </div>
                    )}
                    
                    {item.manufacturer && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Manufacturer:</span>
                        <span className="w-2/3">{item.manufacturer}</span>
                      </div>
                    )}
                    
                    {item.sku && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">SKU:</span>
                        <span className="w-2/3">{item.sku}</span>
                      </div>
                    )}
                    
                    {item.upc && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">UPC:</span>
                        <span className="w-2/3">{item.upc}</span>
                      </div>
                    )}
                    
                    {item.store_id && (
                      <div className="flex">
                        <span className="font-medium w-1/3 text-gray-600">Store ID:</span>
                        <span className="w-2/3">{item.store_id}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Product attributes */}
                  {item.attributes && Object.keys(item.attributes).length > 0 && (
                    <div className="mt-6 space-y-3">
                      <h3 className="text-lg font-semibold border-b pb-2">Product Attributes</h3>
                      
                      {Object.entries(item.attributes).map(([key, value], index) => {
                        if (typeof value === 'object' && value !== null) return null;
                        
                        return (
                          <div key={index} className="flex">
                            <span className="font-medium w-1/3 text-gray-600">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                            </span>
                            <span className="w-2/3">
                              {value === true ? 'Yes' : 
                               value === false ? 'No' : 
                               value === null ? 'N/A' : 
                               String(value)}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </>
              )}
              
              {/* Tags */}
              {item.tags && item.tags.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold border-b pb-2 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {item.tags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                        🏷️ {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="mt-8 grid grid-cols-2 gap-4 border-t border-purple-200 pt-6">
            {isProduct(item) && (
              <>
                <button className="bg-gray-600 text-white py-2 px-4 rounded-md font-medium hover:bg-gray-700 focus:ring-2 focus:ring-gray-600">
                  Add to Cart
                </button>
                <button className="bg-purple-300 text-purple-700 py-2 px-4 rounded-md font-medium hover:bg-purple-400 focus:ring-2 focus:ring-purple-300">
                  Save for Later
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemDetailsModal;