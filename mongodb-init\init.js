// MongoDB initialization script
db = db.getSiblingDB('superapp');

// Create collections
db.createCollection('users');
db.createCollection('services');
db.createCollection('usage_logs');
db.createCollection('status_checks');

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 });
db.users.createIndex({ created_at: 1 });

db.services.createIndex({ name: 1 });
db.services.createIndex({ category: 1 });
db.services.createIndex({ status: 1 });
db.services.createIndex({ created_at: 1 });

db.usage_logs.createIndex({ user_id: 1 });
db.usage_logs.createIndex({ service_id: 1 });
db.usage_logs.createIndex({ timestamp: 1 });
db.usage_logs.createIndex({ action: 1 });

db.status_checks.createIndex({ timestamp: 1 });
db.status_checks.createIndex({ client_name: 1 });

// Insert initial sample data
db.services.insertMany([
  {
    id: "svc-001",
    name: "Text Translation",
    description: "Translate text between multiple languages using AI",
    category: "AI",
    endpoint: "/ai/translate",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-002", 
    name: "Image Recognition",
    description: "Analyze and recognize objects in images",
    category: "AI",
    endpoint: "/ai/analyze-image",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-003",
    name: "Code Generation",
    description: "Generate code snippets and programs",
    category: "Developer",
    endpoint: "/ai/generate-code",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-004",
    name: "Audio Processing",
    description: "Process and analyze audio files",
    category: "Media",
    endpoint: "/ai/process-audio",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-005",
    name: "Video Analysis",
    description: "Analyze video content and extract insights",
    category: "Media", 
    endpoint: "/ai/analyze-video",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-006",
    name: "Search Services",
    description: "Search through available services and categories",
    category: "Utility",
    endpoint: "/mcp/search_services",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-007",
    name: "Workspace Management",
    description: "Manage user workspace settings and preferences",
    category: "Utility",
    endpoint: "/mcp/manage_workspace",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  },
  {
    id: "svc-008",
    name: "Usage Analytics",
    description: "Analyze service usage patterns and provide insights",
    category: "Analytics",
    endpoint: "/mcp/analyze_usage",
    status: "active",
    created_at: new Date(),
    usage_count: 0
  }
]);

// Insert sample user
db.users.insertOne({
  id: "user-001",
  username: "demo_user",
  email: "<EMAIL>",
  created_at: new Date(),
  is_active: true,
  preferences: {
    theme: "light",
    notifications: true,
    language: "en"
  }
});

print("MongoDB initialized successfully with sample data!");
