services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://backend:8000
      - VITE_AI_URL=http://ai-service:8001
    depends_on:
      - backend
      - ai-service

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - mongodb
      - elasticsearch

  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - "d:/SuperApp-v1/mongodump:/data/db"

  elasticsearch:
    image: elasticsearch:7.17.9
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - "d:/SuperApp-v1/elasticsearch_index_backup:/usr/share/elasticsearch/snapshots"
      - "d:/SuperApp-v1/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml"

volumes:
  mongodb_data:
  elasticsearch_data: