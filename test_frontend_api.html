<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Frontend API Connection Test</h1>
    
    <button onclick="testAIService()">Test AI Service</button>
    <button onclick="testBackend()">Test Backend</button>
    <button onclick="testChatEndpoint()">Test Chat Endpoint</button>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(div);
        }
        
        async function testAIService() {
            try {
                addResult('Testing AI Service health endpoint...');
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                addResult(`AI Service Health: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                addResult(`AI Service Error: ${error.message}`, false);
            }
        }
        
        async function testBackend() {
            try {
                addResult('Testing Backend health endpoint...');
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                addResult(`Backend Health: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                addResult(`Backend Error: ${error.message}`, false);
            }
        }
        
        async function testChatEndpoint() {
            try {
                addResult('Testing AI Chat endpoint...');
                const response = await fetch('http://localhost:8001/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            {
                                role: 'user',
                                content: 'Hello from frontend test!'
                            }
                        ],
                        context: {
                            browsing: false,
                            tools: false
                        }
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`Chat Response: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    addResult(`Chat Error (${response.status}): ${errorText}`, false);
                }
            } catch (error) {
                addResult(`Chat Error: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
