#!/usr/bin/env python3
"""
Test script to debug the search API issue
"""
import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from app.modules.search.service import SearchService
from app.modules.search.schemas import SearchRequest, SearchType

async def test_search():
    """Test the search service directly"""
    try:
        # Create search service
        search_service = SearchService()
        
        # Create search request
        search_request = SearchRequest(
            query="coffee",
            type=SearchType.ALL,
            page=1,
            per_page=10
        )
        
        print("Testing search service...")
        print(f"Search request: {search_request}")
        
        # Perform search
        result = await search_service.search(search_request)
        
        print(f"Search result: {result}")
        print(f"Total items found: {result.total}")
        print(f"Items returned: {len(result.items)}")
        
        if result.items:
            print("First item:")
            print(result.items[0])
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_search())
