import os
import httpx
import asyncio
from fastapi import FastAP<PERSON>, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
import json
from datetime import datetime
import redis.asyncio as redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="SuperApp API Gateway", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer(auto_error=False)

# Service URLs
BACKEND_SERVICE_URL = os.getenv("BACKEND_SERVICE_URL", "http://localhost:5000")
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:8001")
AI_SERVICE_URL = os.getenv("AI_SERVICE_URL", "http://localhost:8002")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Redis client
redis_client = None

@app.on_event("startup")
async def startup_event():
    global redis_client
    try:
        redis_client = redis.from_url(REDIS_URL, decode_responses=True)
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    if redis_client:
        await redis_client.close()

# Request/Response Models
class ServiceRequest(BaseModel):
    method: str = "GET"
    path: str
    data: Optional[Dict[Any, Any]] = None
    params: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None

class ServiceResponse(BaseModel):
    status_code: int
    data: Any
    headers: Optional[Dict[str, str]] = None

# Middleware for logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.utcnow()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    response = await call_next(request)
    
    # Log response
    process_time = (datetime.utcnow() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")
    
    return response

# Authentication middleware
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        return None
    
    # Simple token validation - in production, validate JWT properly
    token = credentials.credentials
    if token.startswith("Bearer "):
        token = token[7:]
    
    # Cache user info in Redis for performance
    if redis_client:
        try:
            user_info = await redis_client.get(f"user:{token}")
            if user_info:
                return json.loads(user_info)
        except Exception as e:
            logger.error(f"Redis error: {e}")
    
    return {"user_id": "anonymous", "token": token}

# Service proxy function
async def proxy_request(service_url: str, path: str, method: str = "GET", 
                       data: Optional[Dict] = None, params: Optional[Dict] = None,
                       headers: Optional[Dict] = None) -> ServiceResponse:
    """Proxy requests to microservices"""
    
    full_url = f"{service_url}{path}"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.request(
                method=method,
                url=full_url,
                json=data if method in ["POST", "PUT", "PATCH"] else None,
                params=params,
                headers=headers or {}
            )
            
            return ServiceResponse(
                status_code=response.status_code,
                data=response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                headers=dict(response.headers)
            )
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Service timeout")
    except httpx.RequestError as e:
        logger.error(f"Request error: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = {}
    
    # Check each service
    services = {
        "backend": BACKEND_SERVICE_URL,
        "mcp-server": MCP_SERVER_URL,
        "ai-service": AI_SERVICE_URL
    }
    
    for name, url in services.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{url}/health")
                services_status[name] = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "response_time": response.elapsed.total_seconds()
                }
        except Exception as e:
            services_status[name] = {
                "status": "unhealthy",
                "error": str(e)
            }
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": services_status
    }

# Backend service routes
@app.api_route("/api/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_backend(path: str, request: Request, current_user = Depends(get_current_user)):
    """Proxy requests to backend service"""
    
    # Get request data
    data = None
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            data = await request.json()
        except:
            data = None
    
    # Add user context to headers
    headers = dict(request.headers)
    if current_user:
        headers["X-User-ID"] = str(current_user.get("user_id", "anonymous"))
        headers["X-User-Token"] = current_user.get("token", "")
    
    response = await proxy_request(
        service_url=BACKEND_SERVICE_URL,
        path=f"/api/{path}",
        method=request.method,
        data=data,
        params=dict(request.query_params),
        headers=headers
    )
    
    if response.status_code >= 400:
        raise HTTPException(status_code=response.status_code, detail=response.data)
    
    return response.data

# MCP Server routes
@app.api_route("/mcp/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def proxy_mcp(path: str, request: Request, current_user = Depends(get_current_user)):
    """Proxy requests to MCP server"""
    
    data = None
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            data = await request.json()
        except:
            data = None
    
    headers = dict(request.headers)
    if current_user:
        headers["X-User-ID"] = str(current_user.get("user_id", "anonymous"))
    
    response = await proxy_request(
        service_url=MCP_SERVER_URL,
        path=f"/{path}",
        method=request.method,
        data=data,
        params=dict(request.query_params),
        headers=headers
    )
    
    if response.status_code >= 400:
        raise HTTPException(status_code=response.status_code, detail=response.data)
    
    return response.data

# AI Service routes
@app.api_route("/ai/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def proxy_ai(path: str, request: Request, current_user = Depends(get_current_user)):
    """Proxy requests to AI service"""
    
    data = None
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            data = await request.json()
        except:
            data = None
    
    headers = dict(request.headers)
    if current_user:
        headers["X-User-ID"] = str(current_user.get("user_id", "anonymous"))
    
    response = await proxy_request(
        service_url=AI_SERVICE_URL,
        path=f"/{path}",
        method=request.method,
        data=data,
        params=dict(request.query_params),
        headers=headers
    )
    
    if response.status_code >= 400:
        raise HTTPException(status_code=response.status_code, detail=response.data)
    
    return response.data

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "SuperApp API Gateway",
        "version": "1.0.0",
        "services": {
            "backend": f"{BACKEND_SERVICE_URL}/api",
            "mcp": f"{MCP_SERVER_URL}",
            "ai": f"{AI_SERVICE_URL}"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
