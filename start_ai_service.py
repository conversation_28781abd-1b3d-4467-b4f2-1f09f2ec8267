#!/usr/bin/env python3
"""
Start AI service with error handling
"""
import sys
import os
import traceback

# Add the ai-service directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ai-service'))

try:
    print("Starting AI service...")
    print("Python version:", sys.version)
    print("Current directory:", os.getcwd())
    
    # Import the main module
    print("Importing main module...")
    import main
    print("Main module imported successfully")
    
    # Import uvicorn
    print("Importing uvicorn...")
    import uvicorn
    print("Uvicorn imported successfully")
    
    # Start the server
    print("Starting uvicorn server on 127.0.0.1:8001...")
    uvicorn.run(main.app, host="127.0.0.1", port=8001, log_level="info")
    
except Exception as e:
    print(f"Error starting AI service: {e}")
    print("Traceback:")
    traceback.print_exc()
    sys.exit(1)
