import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPEx<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import redis.asyncio as redis
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="SuperApp MCP Server", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:5000")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Redis client
redis_client = None

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str = "anonymous"):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id not in self.user_connections:
            self.user_connections[user_id] = []
        self.user_connections[user_id].append(websocket)
        logger.info(f"User {user_id} connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket, user_id: str = "anonymous"):
        self.active_connections.remove(websocket)
        if user_id in self.user_connections:
            self.user_connections[user_id].remove(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        logger.info(f"User {user_id} disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_user_message(self, message: str, user_id: str):
        if user_id in self.user_connections:
            for connection in self.user_connections[user_id]:
                await connection.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

@app.on_event("startup")
async def startup_event():
    global redis_client
    try:
        redis_client = redis.from_url(REDIS_URL, decode_responses=True)
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    if redis_client:
        await redis_client.close()

# MCP Models
class MCPRequest(BaseModel):
    jsonrpc: str = "2.0"
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    method: str
    params: Optional[Dict[str, Any]] = None

class MCPResponse(BaseModel):
    jsonrpc: str = "2.0"
    id: str
    result: Optional[Any] = None
    error: Optional[Dict[str, Any]] = None

class MCPTool(BaseModel):
    name: str
    description: str
    input_schema: Dict[str, Any]

class MCPResource(BaseModel):
    uri: str
    name: str
    description: Optional[str] = None
    mime_type: Optional[str] = None

class WorkspaceSettings(BaseModel):
    user_id: str
    preferences: Dict[str, Any]
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class UsageAnalytics(BaseModel):
    service_id: str
    user_id: str
    action: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = None

# MCP Core Methods
async def handle_initialize(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle MCP initialize request"""
    return {
        "protocolVersion": "2024-11-05",
        "capabilities": {
            "tools": {
                "listChanged": True
            },
            "resources": {
                "subscribe": True,
                "listChanged": True
            },
            "prompts": {
                "listChanged": True
            },
            "logging": {}
        },
        "serverInfo": {
            "name": "SuperApp MCP Server",
            "version": "1.0.0"
        }
    }

async def handle_tools_list(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tools/list request"""
    tools = [
        MCPTool(
            name="search_services",
            description="Search through available services and categories in SuperApp",
            input_schema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to find services"
                    },
                    "category": {
                        "type": "string",
                        "description": "Optional category to filter results"
                    }
                },
                "required": ["query"]
            }
        ),
        MCPTool(
            name="manage_workspace",
            description="Manage user workspace settings and preferences",
            input_schema={
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": ["get", "update", "delete"],
                        "description": "Action to perform on workspace settings"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "User ID for workspace management"
                    },
                    "settings": {
                        "type": "object",
                        "description": "Workspace settings to manage"
                    }
                },
                "required": ["action", "user_id"]
            }
        ),
        MCPTool(
            name="analyze_usage",
            description="Analyze service usage patterns and provide insights",
            input_schema={
                "type": "object",
                "properties": {
                    "timeframe": {
                        "type": "string",
                        "description": "Time period for analysis (e.g. day, week, month)"
                    },
                    "service_id": {
                        "type": "string",
                        "description": "Optional service ID to analyze"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "User ID for personalized analytics"
                    }
                }
            }
        ),
        MCPTool(
            name="execute_ai_task",
            description="Execute AI-powered tasks using various modalities",
            input_schema={
                "type": "object",
                "properties": {
                    "task_type": {
                        "type": "string",
                        "enum": ["text", "image", "audio", "video", "code"],
                        "description": "Type of AI task to execute"
                    },
                    "input_data": {
                        "type": "string",
                        "description": "Input data for the AI task"
                    },
                    "parameters": {
                        "type": "object",
                        "description": "Additional parameters for the AI task"
                    }
                },
                "required": ["task_type", "input_data"]
            }
        )
    ]
    
    return {"tools": [tool.dict() for tool in tools]}

async def handle_tools_call(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tools/call request"""
    tool_name = params.get("name")
    arguments = params.get("arguments", {})
    
    try:
        if tool_name == "search_services":
            return await search_services_tool(arguments)
        elif tool_name == "manage_workspace":
            return await manage_workspace_tool(arguments)
        elif tool_name == "analyze_usage":
            return await analyze_usage_tool(arguments)
        elif tool_name == "execute_ai_task":
            return await execute_ai_task_tool(arguments)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown tool: {tool_name}")
    except Exception as e:
        logger.error(f"Error executing tool {tool_name}: {e}")
        return {
            "content": [
                {
                    "type": "text",
                    "text": f"Error executing tool: {str(e)}"
                }
            ],
            "isError": True
        }

# Tool implementations
async def search_services_tool(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Search services tool implementation"""
    query = arguments.get("query", "")
    category = arguments.get("category")
    
    # Mock service search - in real implementation, integrate with Elasticsearch
    services = [
        {"id": "svc1", "name": "Text Translation", "category": "AI", "description": "Translate text between languages"},
        {"id": "svc2", "name": "Image Recognition", "category": "AI", "description": "Analyze and recognize objects in images"},
        {"id": "svc3", "name": "Code Generation", "category": "Developer", "description": "Generate code snippets"},
        {"id": "svc4", "name": "Audio Processing", "category": "Media", "description": "Process and analyze audio files"},
        {"id": "svc5", "name": "Video Analysis", "category": "Media", "description": "Analyze video content"}
    ]
    
    # Filter services based on query and category
    filtered_services = []
    for service in services:
        if query.lower() in service["name"].lower() or query.lower() in service["description"].lower():
            if not category or service["category"].lower() == category.lower():
                filtered_services.append(service)
    
    return {
        "content": [
            {
                "type": "text",
                "text": f"Found {len(filtered_services)} services matching '{query}'"
            },
            {
                "type": "text",
                "text": json.dumps(filtered_services, indent=2)
            }
        ]
    }

async def manage_workspace_tool(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Manage workspace tool implementation"""
    action = arguments.get("action")
    user_id = arguments.get("user_id")
    settings = arguments.get("settings", {})
    
    if not redis_client:
        return {
            "content": [{"type": "text", "text": "Redis not available"}],
            "isError": True
        }
    
    workspace_key = f"workspace:{user_id}"
    
    try:
        if action == "get":
            workspace_data = await redis_client.get(workspace_key)
            if workspace_data:
                workspace = json.loads(workspace_data)
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Workspace settings for user {user_id}:"
                        },
                        {
                            "type": "text",
                            "text": json.dumps(workspace, indent=2)
                        }
                    ]
                }
            else:
                return {
                    "content": [{"type": "text", "text": f"No workspace found for user {user_id}"}]
                }
        
        elif action == "update":
            workspace = WorkspaceSettings(
                user_id=user_id,
                preferences=settings,
                updated_at=datetime.utcnow()
            )
            await redis_client.set(workspace_key, workspace.json())
            return {
                "content": [{"type": "text", "text": f"Workspace updated for user {user_id}"}]
            }
        
        elif action == "delete":
            await redis_client.delete(workspace_key)
            return {
                "content": [{"type": "text", "text": f"Workspace deleted for user {user_id}"}]
            }
        
    except Exception as e:
        return {
            "content": [{"type": "text", "text": f"Error managing workspace: {str(e)}"}],
            "isError": True
        }

async def analyze_usage_tool(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze usage tool implementation"""
    timeframe = arguments.get("timeframe", "day")
    service_id = arguments.get("service_id")
    user_id = arguments.get("user_id")
    
    # Mock analytics data - in real implementation, query from database
    analytics_data = {
        "timeframe": timeframe,
        "total_requests": 1250,
        "unique_users": 89,
        "top_services": [
            {"service": "Text Translation", "usage": 450},
            {"service": "Image Recognition", "usage": 320},
            {"service": "Code Generation", "usage": 280}
        ],
        "user_specific": {
            "requests": 45,
            "favorite_service": "Text Translation",
            "last_activity": datetime.utcnow().isoformat()
        } if user_id else None
    }
    
    return {
        "content": [
            {
                "type": "text",
                "text": f"Usage analytics for {timeframe}:"
            },
            {
                "type": "text",
                "text": json.dumps(analytics_data, indent=2, default=str)
            }
        ]
    }

async def execute_ai_task_tool(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute AI task tool implementation"""
    task_type = arguments.get("task_type")
    input_data = arguments.get("input_data")
    parameters = arguments.get("parameters", {})
    
    # Forward to AI service
    try:
        async with httpx.AsyncClient() as client:
            ai_service_url = "http://ai-service:8002"  # Use service name for internal communication
            response = await client.post(
                f"{ai_service_url}/execute",
                json={
                    "task_type": task_type,
                    "input_data": input_data,
                    "parameters": parameters
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"AI task completed successfully:"
                        },
                        {
                            "type": "text",
                            "text": json.dumps(result, indent=2)
                        }
                    ]
                }
            else:
                return {
                    "content": [{"type": "text", "text": f"AI service error: {response.text}"}],
                    "isError": True
                }
    except Exception as e:
        return {
            "content": [{"type": "text", "text": f"Error executing AI task: {str(e)}"}],
            "isError": True
        }

# MCP Protocol Endpoints
@app.post("/mcp")
async def mcp_handler(request: MCPRequest):
    """Main MCP protocol handler"""
    try:
        if request.method == "initialize":
            result = await handle_initialize(request.params or {})
        elif request.method == "tools/list":
            result = await handle_tools_list(request.params or {})
        elif request.method == "tools/call":
            result = await handle_tools_call(request.params or {})
        else:
            raise HTTPException(status_code=400, detail=f"Unknown method: {request.method}")
        
        return MCPResponse(id=request.id, result=result)
    
    except Exception as e:
        logger.error(f"MCP error: {e}")
        return MCPResponse(
            id=request.id,
            error={
                "code": -1,
                "message": str(e)
            }
        )

# WebSocket endpoint for real-time communication
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Handle different message types
            if message_data.get("type") == "mcp_request":
                # Process MCP request via WebSocket
                mcp_request = MCPRequest(**message_data.get("data", {}))
                response = await mcp_handler(mcp_request)
                await manager.send_personal_message(
                    json.dumps({
                        "type": "mcp_response",
                        "data": response.dict()
                    }),
                    websocket
                )
            elif message_data.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong"}),
                    websocket
                )
            
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)

# Health check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "active_connections": len(manager.active_connections)
    }

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "SuperApp MCP Server",
        "version": "1.0.0",
        "protocol": "Model Context Protocol 2024-11-05",
        "capabilities": ["tools", "resources", "prompts", "logging"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
