# SuperApp: Everything App Monorepo

## Overview
A modular, all-in-one business suite inspired by Zoho One and Odoo, featuring:
- Vite + React + TypeScript + Tailwind CSS frontend
- Python FastAPI backend (modular, microservices)
- A2A model service (provisioned, downloadable)
- Search engine (Elasticsearch)
- PostgreSQL database
- Docker Compose for single-node deployment

## Project Structure
```
/frontend         # Vite + React + TS + Tailwind
/backend          # FastAPI (Python)
  /a2a_service    # A2A model microservice
  /search_service # Search engine API
  /modules        # Business modules (CRM, Accounting, etc.)
/db               # PostgreSQL, Elasticsearch, Redis
/docker-compose.yml
```

## Quick Start
1. Clone the repo
2. Run `docker-compose up --build`
3. Access the frontend at `http://localhost:3000`

## Services
- **Frontend:** Vite + React + TypeScript + Tailwind CSS
- **Backend:** FastAPI (Python)
- **A2A Model Service:** API for model download/inference
- **Search Service:** Elasticsearch-powered search
- **Database:** PostgreSQL

## Business Modules
- CRM, Accounting, HR, Inventory, Project Management, etc. (modular, microservices)

## Development
- All services run in Docker containers
- Hot reload for frontend and backend

---

## License
MIT 