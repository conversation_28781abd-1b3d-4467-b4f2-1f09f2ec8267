import React from 'react';
import { Heart, Share2 } from 'lucide-react';
import { Item } from '../services/api';

interface ProductCardProps {
  item: Item;
  onViewDetails: (item: Item) => void;
  onBuyNow?: (item: Item) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ item, onViewDetails, onBuyNow }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative h-48 bg-gray-200">
        {item.images && item.images[0] ? (
          <img
            src={item.images[0].url}
            alt={item.images[0].alt || item.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            No Image
          </div>
        )}
        {item.type === 'service' && (
          <div className="absolute top-2 right-2 bg-purple-400 text-white px-2 py-1 rounded-full text-xs">
            Service
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{item.name}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{item.description}</p>

        {/* Price */}
        {item.price && (
          <div className="flex items-baseline mb-4">
            <span className="text-2xl font-bold text-gray-900">
              {item.price.currency} {item.price.amount.toFixed(2)}
            </span>
            {item.price.discount_percentage && (
              <span className="ml-2 text-sm text-green-600">
                ({item.price.discount_percentage}% OFF)
              </span>
            )}
          </div>
        )}

        {/* Tags */}
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {item.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => onViewDetails(item)}
            className="bg-purple-400 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors"
          >
            View Details
          </button>
          <button
            onClick={() => onBuyNow?.(item)}
            className="bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors"
          >
            Buy Now
          </button>
        </div>
      </div>

      {/* Location and Seller Info */}
      <div className="px-4 py-3 flex justify-between items-center border-t border-gray-200">
        <div className="flex items-start">
          <div className="text-red-500 mr-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"></path>
            </svg>
          </div>
          <div>
            {item.location?.distance && (
              <div className="font-medium">
                {typeof item.location.distance === 'object'
                  ? `${item.location.distance.value} ${item.location.distance.unit}`
                  : item.location.distance
                }
              </div>
            )}
            <div className="text-xs">Offered By</div>
            <div className="font-medium">{item.seller || 'Unknown Seller'}</div>
          </div>
        </div>
        <div>
          <div className="text-sm mb-1">Get it by Today@ 5PM</div>
          <div className="text-xs text-blue-600 text-right">Disclaimer</div>
        </div>
      </div>

      {/* Share and like buttons */}
      <div className="px-4 py-2 flex justify-between border-t border-purple-200">
        <button className="text-purple-400 hover:text-purple-700">
          <Share2 className="w-6 h-6" />
        </button>
        <button className="text-purple-400 hover:text-purple-400">
          <Heart className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
