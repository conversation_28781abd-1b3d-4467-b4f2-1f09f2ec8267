"""
SuperApp Backend Application Package
"""

__version__ = "1.0.0"

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="SuperApp Backend", version="1.0.0")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include routers
from app.modules.search import router as search_router

# Mount search router
app.include_router(search_router.router, prefix="/api/search", tags=["Search"])

@app.get("/")
async def root():
    return {
        "app": "SuperApp Backend",
        "version": "1.0.0",
        "status": "running"
    } 