from datetime import datetime
from typing import List, Optional
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
from app.modules.sales.schemas import (
    Customer,
    CustomerCreate,
    Appointment,
    AppointmentCreate,
    SalesLead,
    SalesLeadCreate,
    LeadStatus
)

class SalesService:
    def __init__(self):
        self.client = AsyncIOMotorClient(settings.MONGODB_URL)
        self.db = self.client[settings.MONGODB_DB]
        self.customers = self.db.customers
        self.appointments = self.db.appointments
        self.leads = self.db.leads
        self.sales = self.db.sales

    async def create_customer(self, customer: CustomerCreate) -> Customer:
        now = datetime.utcnow()
        customer_dict = customer.model_dump()
        customer_dict.update({
            "_id": str(ObjectId()),
            "created_at": now,
            "updated_at": now
        })
        await self.customers.insert_one(customer_dict)
        return Customer(**customer_dict)

    async def list_customers(self, skip: int = 0, limit: int = 100) -> List[Customer]:
        cursor = self.customers.find().skip(skip).limit(limit)
        customers = await cursor.to_list(length=limit)
        return [Customer(**customer) for customer in customers]

    async def get_customer(self, customer_id: str) -> Optional[Customer]:
        customer = await self.customers.find_one({"_id": customer_id})
        if customer:
            return Customer(**customer)
        return None

    async def create_appointment(self, appointment: AppointmentCreate) -> Appointment:
        now = datetime.utcnow()
        appointment_dict = appointment.model_dump()
        appointment_dict.update({
            "_id": str(ObjectId()),
            "created_at": now,
            "updated_at": now
        })
        await self.appointments.insert_one(appointment_dict)
        return Appointment(**appointment_dict)

    async def list_appointments(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        customer_id: Optional[str] = None
    ) -> List[Appointment]:
        query = {}
        if start_date and end_date:
            query["start_time"] = {"$gte": start_date, "$lte": end_date}
        if customer_id:
            query["customer_id"] = customer_id

        cursor = self.appointments.find(query)
        appointments = await cursor.to_list(length=100)
        return [Appointment(**appointment) for appointment in appointments]

    async def create_lead(self, lead: SalesLeadCreate) -> SalesLead:
        now = datetime.utcnow()
        lead_dict = lead.model_dump()
        lead_dict.update({
            "_id": str(ObjectId()),
            "status": LeadStatus.NEW,
            "created_at": now,
            "updated_at": now,
            "last_contact_date": now
        })
        await self.leads.insert_one(lead_dict)
        return SalesLead(**lead_dict)

    async def list_leads(self, status: Optional[str] = None) -> List[SalesLead]:
        query = {}
        if status:
            query["status"] = status

        cursor = self.leads.find(query)
        leads = await cursor.to_list(length=100)
        return [SalesLead(**lead) for lead in leads]

    async def update_lead_status(self, lead_id: str, status: str) -> Optional[SalesLead]:
        now = datetime.utcnow()
        update_result = await self.leads.update_one(
            {"_id": lead_id},
            {
                "$set": {
                    "status": status,
                    "updated_at": now,
                    "last_contact_date": now
                }
            }
        )
        if update_result.modified_count:
            lead = await self.leads.find_one({"_id": lead_id})
            return SalesLead(**lead)
        return None

    async def get_sales(self) -> List[dict]:
        cursor = self.sales.find({})
        sales = await cursor.to_list(length=100)
        return sales

    async def create_sale(self, sale_data: dict) -> dict:
        sale_data["created_at"] = datetime.utcnow()
        result = await self.sales.insert_one(sale_data)
        sale_data["_id"] = str(result.inserted_id)
        return sale_data

    async def get_sale(self, sale_id: str) -> Optional[dict]:
        sale = await self.sales.find_one({"_id": sale_id})
        return sale if sale else None

    async def update_sale(self, sale_id: str, sale_data: dict) -> Optional[dict]:
        sale_data["updated_at"] = datetime.utcnow()
        result = await self.sales.update_one(
            {"_id": sale_id},
            {"$set": sale_data}
        )
        if result.modified_count:
            return await self.get_sale(sale_id)
        return None

    async def delete_sale(self, sale_id: str) -> bool:
        result = await self.sales.delete_one({"_id": sale_id})
        return result.deleted_count > 0 