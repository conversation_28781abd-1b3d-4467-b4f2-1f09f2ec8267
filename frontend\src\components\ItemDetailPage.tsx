import React, { useState } from 'react';
import { Heart, Share2, MapPin, Star, Clock, Tag, ArrowLeft, ShoppingCart } from 'lucide-react';
import { Item } from '../services/api';
import AIChatInterface from './AIChatInterface';

interface ItemDetailPageProps {
  item: Item | null;
  loading: boolean;
  onBack: () => void;
}

const ItemDetailPage: React.FC<ItemDetailPageProps> = ({
  item,
  loading,
  onBack
}) => {
  const [showAIChat, setShowAIChat] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  // Get primary image URL
  const getPrimaryImage = (item: Item) => {
    if (!item?.images || item.images.length === 0) {
      return '/api/placeholder/400/400';
    }
    const primaryImage = item.images.find(img => img.is_primary);
    return primaryImage ? primaryImage.url : item.images[0].url;
  };

  // Format price
  const formatPrice = (item: Item) => {
    if (!item.price) return 'Price not available';
    const { amount, currency = 'INR', discount_percentage, original_amount } = item.price;
    return (
      <div className="flex items-baseline gap-2">
        <span className="text-3xl font-bold text-gray-900">
          {currency} {amount.toFixed(2)}
        </span>
        {discount_percentage && original_amount && (
          <>
            <span className="text-lg text-gray-500 line-through">
              {currency} {original_amount.toFixed(2)}
            </span>
            <span className="text-green-600">
              ({discount_percentage}% OFF)
            </span>
          </>
        )}
      </div>
    );
  };

  if (loading || !item) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="h-96 bg-gray-200 rounded"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-12 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Back button and breadcrumbs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Search Results
          </button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Image Gallery */}
          <div className="space-y-4">
            <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={item.images?.[selectedImage]?.url || getPrimaryImage(item)}
                alt={item.name}
                className="w-full h-full object-center object-cover"
              />
            </div>
            {item.images && item.images.length > 1 && (
              <div className="grid grid-cols-6 gap-2">
                {item.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-w-1 aspect-h-1 rounded-md overflow-hidden ${
                      selectedImage === index ? 'ring-2 ring-purple-400' : ''
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={image.alt_text || `${item.name} view ${index + 1}`}
                      className="w-full h-full object-center object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{item.name}</h1>
              <p className="mt-2 text-gray-500">{item.description}</p>
            </div>

            {/* Price */}
            <div className="border-t border-b border-gray-200 py-4">
              {formatPrice(item)}
            </div>

            {/* Tags */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {item.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                  >
                    <Tag className="w-4 h-4 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* Location */}
            {item.location && (
              <div className="flex items-start gap-2 text-gray-600">
                <MapPin className="w-5 h-5 flex-shrink-0 mt-0.5" />
                <div>
                  {item.location.address && <div>{item.location.address}</div>}
                  <div>
                    {[
                      item.location.city,
                      item.location.state,
                      item.location.country,
                      item.location.zip_code
                    ]
                      .filter(Boolean)
                      .join(', ')}
                  </div>
                </div>
              </div>
            )}

            {/* Rating */}
            {item.rating && (
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(item.rating!.average)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  ({item.rating.count} reviews)
                </span>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <button className="flex-1 bg-purple-400 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center">
                <ShoppingCart className="w-5 h-5 mr-2" />
                Add to Cart
              </button>
              <button className="p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                <Heart className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                <Share2 className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            {/* Delivery Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 text-gray-600">
                <Clock className="w-5 h-5" />
                <span>Get it by Today@ 5PM</span>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                Free delivery for orders above INR 500
              </div>
            </div>

            {/* AI Chat Button */}
            <button
              onClick={() => setShowAIChat(!showAIChat)}
              className="w-full mt-4 bg-gradient-to-r from-purple-400 to-indigo-600 text-white px-6 py-3 rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors"
            >
              Ask AI About This Item
            </button>
          </div>
        </div>

        {/* AI Chat Interface */}
        {showAIChat && (
          <div className="mt-8 border border-gray-200 rounded-lg overflow-hidden">
            <AIChatInterface />
          </div>
        )}
      </div>
    </div>
  );
};

export default ItemDetailPage; 