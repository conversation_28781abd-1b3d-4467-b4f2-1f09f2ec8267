{"name": "my-ui-components", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "lucide-react": "^0.484.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.0.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.0.16", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}