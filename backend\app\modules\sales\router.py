from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import datetime
from app.modules.sales.schemas import (
    Customer,
    CustomerCreate,
    Appointment,
    AppointmentCreate,
    SalesLead,
    SalesLeadCreate
)
from app.modules.sales.service import SalesService

router = APIRouter()
sales_service = SalesService()

# CRM Endpoints
@router.post("/customers/", response_model=Customer)
async def create_customer(customer: CustomerCreate):
    """Create a new customer in the CRM system"""
    return await sales_service.create_customer(customer)

@router.get("/customers/", response_model=List[Customer])
async def list_customers(skip: int = 0, limit: int = 100):
    """List all customers with pagination"""
    return await sales_service.list_customers(skip, limit)

@router.get("/customers/{customer_id}", response_model=Customer)
async def get_customer(customer_id: str):
    """Get customer details by ID"""
    customer = await sales_service.get_customer(customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

# Appointment Scheduling Endpoints
@router.post("/appointments/", response_model=Appointment)
async def create_appointment(appointment: AppointmentCreate):
    """Schedule a new appointment"""
    return await sales_service.create_appointment(appointment)

@router.get("/appointments/", response_model=List[Appointment])
async def list_appointments(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    customer_id: Optional[str] = None
):
    """List appointments with optional filters"""
    return await sales_service.list_appointments(start_date, end_date, customer_id)

# Sales Lead Management
@router.post("/leads/", response_model=SalesLead)
async def create_lead(lead: SalesLeadCreate):
    """Create a new sales lead"""
    return await sales_service.create_lead(lead)

@router.get("/leads/", response_model=List[SalesLead])
async def list_leads(status: Optional[str] = None):
    """List all sales leads with optional status filter"""
    return await sales_service.list_leads(status)

@router.put("/leads/{lead_id}/status", response_model=SalesLead)
async def update_lead_status(lead_id: str, status: str):
    """Update the status of a sales lead"""
    return await sales_service.update_lead_status(lead_id, status) 