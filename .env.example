# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Node environment (development/production)
NODE_ENV=development

# Port configuration
PORT=80

# API URLs (for production use service names, for development use localhost)
VITE_API_URL=http://localhost:8000
VITE_AI_SERVICE_URL=http://localhost:8001

# API Keys (Get these from respective providers)
OPENAI_API_KEY=your_openai_api_key_here
SARVAM_API_KEY=your_sarvam_api_key_here

# Database URLs (use service names for Docker, localhost for local development)
MONGODB_URL=mongodb://localhost:27017
ELASTICSEARCH_URL=http://localhost:9200
REDIS_URL=redis://localhost:6379

# Optional: Database names
MONGODB_DATABASE=superapp
ELASTICSEARCH_INDEX=superapp

# Optional: Security
JWT_SECRET=your_jwt_secret_here
CORS_ORIGINS=http://localhost:3000,http://localhost:80
