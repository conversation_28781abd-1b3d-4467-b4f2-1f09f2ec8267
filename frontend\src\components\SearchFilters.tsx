import React, { useRef, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { FaSort, FaFilter, FaMoneyBill, FaStar } from 'react-icons/fa';

export interface FilterState {
  activeButton: 'sort' | 'filter' | 'price' | 'rating' | null;
  sortBy?: 'name_asc' | 'name_desc' | 'price_asc' | 'price_desc';
  filters: {
    inStock: boolean;
    onSale: boolean;
  };
  priceRange: {
    min: number | null;
    max: number | null;
  };
  rating: number | null;
}

interface FilterContentProps {
  type: FilterState['activeButton'];
}

interface FilterContentProps {
  type: FilterState['activeButton'];
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
}

const FilterContent: React.FC<FilterContentProps> = ({ type, filters, onFilterChange }) => {
  if (!type) return null;

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    console.log('Updating filters:', updatedFilters); // Debug log
    onFilterChange(updatedFilters);

    // Close dropdown after selection for sort and rating
    if (newFilters.sortBy || newFilters.rating) {
      onFilterChange({ ...updatedFilters, activeButton: null });
    }
  };

  const renderSortOptions = () => (
    <div className="flex flex-col gap-2">
      <button
        className={`text-left px-3 py-2 hover:bg-gray-100 rounded-lg ${filters.sortBy === 'name_asc' ? 'bg-purple-100' : ''}`}
        onClick={() => updateFilters({ sortBy: 'name_asc' })}
      >Name (A-Z)</button>
      <button
        className={`text-left px-3 py-2 hover:bg-gray-100 rounded-lg ${filters.sortBy === 'name_desc' ? 'bg-purple-100' : ''}`}
        onClick={() => updateFilters({ sortBy: 'name_desc' })}
      >Name (Z-A)</button>
      <button
        className={`text-left px-3 py-2 hover:bg-gray-100 rounded-lg ${filters.sortBy === 'price_asc' ? 'bg-purple-100' : ''}`}
        onClick={() => updateFilters({ sortBy: 'price_asc' })}
      >Price (Low to High)</button>
      <button
        className={`text-left px-3 py-2 hover:bg-gray-100 rounded-lg ${filters.sortBy === 'price_desc' ? 'bg-purple-100' : ''}`}
        onClick={() => updateFilters({ sortBy: 'price_desc' })}
      >Price (High to Low)</button>
    </div>
  );

  const renderFilterOptions = () => (
    <div className="flex flex-col gap-2">
      <label className="flex items-center gap-2">
        <input
          type="checkbox"
          className="rounded text-purple-600"
          checked={filters.filters.inStock}
          onChange={(e) => updateFilters({ filters: { ...filters.filters, inStock: e.target.checked } })}
        />
        <span>In Stock</span>
      </label>
      <label className="flex items-center gap-2">
        <input
          type="checkbox"
          className="rounded text-purple-600"
          checked={filters.filters.onSale}
          onChange={(e) => updateFilters({ filters: { ...filters.filters, onSale: e.target.checked } })}
        />
        <span>On Sale</span>
      </label>
    </div>
  );

  const renderPriceOptions = () => {
    const [minPrice, setMinPrice] = React.useState<string>(filters.priceRange.min?.toString() || '');
    const [maxPrice, setMaxPrice] = React.useState<string>(filters.priceRange.max?.toString() || '');

    const handleApply = () => {
      updateFilters({
        priceRange: {
          min: minPrice ? Number(minPrice) : null,
          max: maxPrice ? Number(maxPrice) : null
        }
      });
    };

    return (
      <div className="flex flex-col gap-4">
        <div className="flex gap-4 items-center">
          <input
            type="number"
            placeholder="Min"
            className="w-24 px-3 py-1 border rounded-lg"
            min="0"
            value={minPrice}
            onChange={(e) => setMinPrice(e.target.value)}
          />
          <span>to</span>
          <input
            type="number"
            placeholder="Max"
            className="w-24 px-3 py-1 border rounded-lg"
            min="0"
            value={maxPrice}
            onChange={(e) => setMaxPrice(e.target.value)}
          />
        </div>
        <button
          className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
          onClick={handleApply}
        >
          Apply
        </button>
      </div>
    );
  };

  const renderRatingOptions = () => (
    <div className="flex flex-col gap-2">
      {[5, 4, 3, 2, 1].map(rating => (
        <button
          key={rating}
          className={`flex items-center gap-2 px-3 py-2 hover:bg-gray-100 rounded-lg ${filters.rating === rating ? 'bg-purple-100' : ''}`}
          onClick={() => updateFilters({ rating })}
        >
          {Array(rating).fill(null).map((_, i) => (
            <FaStar key={i} className="text-yellow-400 w-4 h-4" />
          ))}
          <span>& Up</span>
        </button>
      ))}
    </div>
  );

  return (
    <div className="p-4 bg-white rounded-lg shadow-lg border border-gray-200">
      {type === 'sort' && renderSortOptions()}
      {type === 'filter' && renderFilterOptions()}
      {type === 'price' && renderPriceOptions()}
      {type === 'rating' && renderRatingOptions()}
    </div>
  );
}

interface SearchFiltersProps {
  onFilterChange: (filters: FilterState) => void;
  activeFilters: FilterState;
}



const FILTER_BUTTONS = [
  { value: 'sort', label: 'Sort', icon: FaSort },
  { value: 'filter', label: 'Filter', icon: FaFilter },
  { value: 'price', label: 'Price', icon: FaMoneyBill },
  { value: 'rating', label: 'Rating', icon: FaStar }
] as const;

const SearchFilters: React.FC<SearchFiltersProps> = ({ onFilterChange, activeFilters }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  const handleButtonClick = (buttonType: FilterState['activeButton']) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 8,
        left: rect.right + window.scrollX - 300 // Adjust width as needed
      });
    }

    onFilterChange({
      ...activeFilters,
      activeButton: activeFilters.activeButton === buttonType ? null : buttonType
    });
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside both the container and the dropdown
      const isOutsideContainer = containerRef.current && !containerRef.current.contains(target);
      const isOutsideDropdown = !document.querySelector('.filter-dropdown')?.contains(target);

      if (isOutsideContainer && isOutsideDropdown && activeFilters.activeButton) {
        onFilterChange({
          ...activeFilters,
          activeButton: null
        });
      }
    };

    if (activeFilters.activeButton) {
      // Use a small delay to prevent immediate closure
      setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeFilters, onFilterChange]);

  return (
    <>
      <div className="relative" ref={containerRef}>
        <div className="flex items-center justify-end gap-4">
          {FILTER_BUTTONS.map(option => (
          <button
            key={option.value}
            onClick={() => handleButtonClick(option.value as FilterState['activeButton'])}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all flex items-center gap-1.5
              ${activeFilters.activeButton === option.value
                ? 'bg-purple-100 text-purple-800 border-2 border-purple-200'
                : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
              }`}
          >
            <option.icon className="w-3.5 h-3.5" />
            {option.label}
          </button>
        ))}
        </div>
      </div>

      {/* Portal Dropdown Overlay */}
      {activeFilters.activeButton && createPortal(
        <div
          className="fixed z-[9999] w-80 filter-dropdown"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left,
          }}
        >
          <FilterContent
            type={activeFilters.activeButton}
            filters={activeFilters}
            onFilterChange={onFilterChange}
          />
        </div>,
        document.body
      )}
    </>
  );
}

export default SearchFilters;