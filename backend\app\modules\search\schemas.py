from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class SearchType(str, Enum):
    ALL = "all"
    PRODUCTS = "products"
    SERVICES = "services"
    STORES = "stores"

class GeoCoordinate(BaseModel):
    lat: float = Field(..., ge=-90, le=90)
    lng: float = Field(..., ge=-180, le=180)

class Location(BaseModel):
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    zip_code: Optional[str] = None
    coordinates: Optional[GeoCoordinate] = None

class Price(BaseModel):
    amount: float
    currency: str
    discount_percentage: Optional[float] = None
    original_amount: Optional[float] = None
    tax_included: Optional[bool] = None

class Rating(BaseModel):
    average: float
    count: int

class Image(BaseModel):
    url: str
    alt: Optional[str] = None

class Item(BaseModel):
    id: str
    name: str
    description: str
    type: str
    category: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    location: Optional[Location] = None
    price: Optional[Price] = None
    rating: Optional[Rating] = None
    images: Optional[List[Image]] = None
    seller: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class SearchRequest(BaseModel):
    query: str
    type: SearchType = SearchType.ALL
    location: Optional[GeoCoordinate] = None
    radius_km: Optional[float] = None
    h3_resolution: Optional[int] = 9
    page: int = 1
    per_page: int = 10

class SearchResponse(BaseModel):
    items: List[Dict[str, Any]]
    total: int
    page: int
    per_page: int 